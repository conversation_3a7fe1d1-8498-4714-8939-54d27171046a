flowchart TD
    Start([User Starts Client]) --> Connect{Connect to Server?}
    Connect -->|Success| ChooseMake[Choose Robot Make<br/>Sniper, Tank, etc.]
    Connect -->|Failed| Error1[Show Connection Error]
    Error1 --> Start
    
    ChooseMake --> EnterName[Enter Robot Name]
    EnterName --> Launch[Launch Robot Command]
    Launch --> ServerCheck{Server Validates?}
    
    ServerCheck -->|Name Taken| Error2[Name Already Exists]
    ServerCheck -->|Success| Spawn[Robot Spawns in World]
    Error2 --> EnterName
    
    Spawn --> MainLoop{Main Game Loop}
    
    MainLoop --> Look[Look Around]
    Look --> ShowWorld[Display World State<br/>- Obstacles<br/>- Other Robots<br/>- Edges]
    
    MainLoop --> Move[Move Robot]
    Move --> ValidateMove{Valid Move?}
    ValidateMove -->|Blocked| ShowError[Show Movement Error]
    ValidateMove -->|Success| UpdatePos[Update Position]
    UpdatePos --> MainLoop
    ShowError --> MainLoop
    
    MainLoop --> Combat[Combat Actions]
    Combat --> Fire{Fire Weapon?}
    Fire -->|No Ammo| NeedReload[Need to Reload]
    Fire -->|Success| CheckHit{Hit Target?}
    CheckHit -->|Hit| DamageTarget[Target Takes Damage]
    CheckHit -->|Miss| MainLoop
    DamageTarget --> TargetDead{Target Destroyed?}
    TargetDead -->|Yes| Victory[Victory Message]
    TargetDead -->|No| MainLoop
    Victory --> MainLoop
    
    NeedReload --> Reload[Reload Weapon]
    Reload --> WaitReload[Wait for Reload Time]
    WaitReload --> MainLoop
    
    MainLoop --> Repair[Repair Shields]
    Repair --> WaitRepair[Wait for Repair Time]
    WaitRepair --> MainLoop
    
    MainLoop --> TakeDamage{Take Damage?}
    TakeDamage -->|Shield Absorbs| MainLoop
    TakeDamage -->|Robot Destroyed| Death[Robot Dies]
    Death --> GameOver[Game Over]
    GameOver --> Restart{Play Again?}
    Restart -->|Yes| Start
    Restart -->|No| Exit([Exit Game])
    
    MainLoop --> Quit[Quit Command]
    Quit --> Exit
