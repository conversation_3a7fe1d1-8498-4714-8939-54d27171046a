sequenceDiagram
    participant User
    participant Client
    participant Server
    participant World
    participant RobotManager
    participant Robot

    User->>Client: Start client with server IP:port
    Client->>Server: TCP Connection Request
    Server-->>Client: Connection Established
    
    User->>Client: launch Sniper "RobotName"
    Client->>Client: Validate command format
    Client->>Server: {"command": "launch", "make": "Sniper", "name": "RobotName"}
    
    Server->>Server: Parse JSON message
    Server->>RobotManager: checkNameAvailable("RobotName")
    RobotManager-->>Server: true/false
    
    alt Name Available
        Server->>World: findSpawnPosition()
        World-->>Server: Position(x, y)
        
        alt Spawn Position Found
            Server->>Robot: new Robot("RobotName", "Sniper", position)
            Robot-->>Server: Robot instance created
            Server->>RobotManager: addRobot(robot)
            RobotManager->>World: placeRobot(robot, position)
            World-->>RobotManager: Robot placed
            RobotManager-->>Server: Robot added successfully
            
            Server-->>Client: {"result": "OK", "data": {"position": [x,y], "state": "NORMAL"}}
            Client-->>User: "Robot launched successfully at [x,y]"
            
        else No Spawn Position
            Server-->>Client: {"result": "ERROR", "message": "No spawn position available"}
            Client-->>User: "Error: World is full"
        end
        
    else Name Taken
        Server-->>Client: {"result": "ERROR", "message": "Name already taken"}
        Client-->>User: "Error: Robot name already exists"
    end
