<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robot World Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        h1 {
            color: #333;
            font-size: 3em;
            margin-bottom: 20px;
            border-bottom: 3px solid #007acc;
            padding-bottom: 20px;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.3em;
            margin-bottom: 40px;
            font-style: italic;
        }
        
        .description {
            color: #555;
            font-size: 1.1em;
            margin-bottom: 40px;
            text-align: left;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #007acc;
        }
        
        .nav-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 40px;
        }
        
        .nav-button {
            background: linear-gradient(135deg, #007acc 0%, #005a9e 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            background: linear-gradient(135deg, #005a9e 0%, #004080 100%);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            text-align: left;
        }
        
        .feature h3 {
            color: #333;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .feature p {
            color: #666;
            margin: 0;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Robot World Documentation</h1>
        <p class="subtitle">Complete Design & Architecture Documentation</p>
        
        <div class="description">
            <p><strong>Welcome to the Robot World documentation hub!</strong> This comprehensive documentation covers all aspects of the Robot World system design, from high-level architecture to detailed interaction flows.</p>
            
            <p>The documentation includes class diagrams, component architectures, user journey flows, system interactions, and state management diagrams - everything you need to understand how the Robot World system works.</p>
        </div>
        
        <div class="nav-buttons">
            <a href="all-diagrams.html" class="nav-button">
                📊 View All Diagrams
            </a>
            <a href="diagrams.html" class="nav-button">
                📋 Legacy Documentation
            </a>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🏗️ System Architecture</h3>
                <p>Comprehensive class diagrams, domain models, and component architectures showing the structural design of both client and server systems.</p>
            </div>
            
            <div class="feature">
                <h3>👤 User Experience</h3>
                <p>User journey storyboards and interaction flows that map out the complete user experience from setup to gameplay.</p>
            </div>
            
            <div class="feature">
                <h3>⚙️ System Flows</h3>
                <p>Detailed process flows for robot movement, combat resolution, and world management operations.</p>
            </div>
            
            <div class="feature">
                <h3>🔄 Interactions</h3>
                <p>Sequence diagrams showing how different components interact during robot launch, combat, and client-server communication.</p>
            </div>
            
            <div class="feature">
                <h3>📊 State Management</h3>
                <p>State diagrams illustrating robot states and world states, including all possible transitions and conditions.</p>
            </div>
            
            <div class="feature">
                <h3>🎯 Interactive Navigation</h3>
                <p>Enhanced navigation with smooth scrolling, active section highlighting, and quick access to any diagram or section.</p>
            </div>

            <div class="feature">
                <h3>🔍 Zoom & Pan Controls</h3>
                <p>Interactive zoom controls for all diagrams with pan support, fullscreen mode, and keyboard shortcuts for better readability.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>📅 Last updated: <span id="last-updated"></span></p>
            <p>🔧 Generated from Mermaid diagram sources</p>
        </div>
    </div>
    
    <script>
        // Set the last updated date
        document.getElementById('last-updated').textContent = new Date().toLocaleDateString();
        
        // Add some interactive behavior
        document.querySelectorAll('.nav-button').forEach(button => {
            button.addEventListener('click', function(e) {
                // Add a subtle loading effect
                this.style.opacity = '0.8';
                setTimeout(() => {
                    this.style.opacity = '1';
                }, 200);
            });
        });
    </script>
</body>
</html>
