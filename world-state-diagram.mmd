stateDiagram-v2
    [*] --> Initializing : Server Start
    
    Initializing --> LoadingConfig : Load Configuration
    LoadingConfig --> ConfigError : Invalid Config
    LoadingConfig --> CreatingWorld : Valid Config
    
    ConfigError --> [*] : Server Exit
    
    CreatingWorld --> PlacingObstacles : World Created
    PlacingObstacles --> ObstacleError : Obstacle Overlap
    PlacingObstacles --> WaitingForClients : Obstacles Placed
    
    ObstacleError --> [*] : Server Exit
    
    WaitingForClients --> Active : First Client Connected
    
    Active --> Processing : Command Received
    Processing --> Active : Command Processed
    
    Active --> Shutdown : Quit Command
    Active --> WaitingForClients : All Clients Disconnected
    
    Shutdown --> [*] : Server Exit
    
    state Active {
        [*] --> Idle
        
        Idle --> RobotLaunching : Launch Command
        RobotLaunching --> Idle : Robot Launched
        RobotLaunching --> Idle : Launch Failed
        
        Idle --> RobotMoving : Move Command
        RobotMoving --> Idle : Movement Complete
        RobotMoving --> RobotDestroyed : Robot Falls in Pit
        
        Idle --> Combat : Fire Command
        Combat --> Idle : Shot Processed
        Combat --> RobotDestroyed : Robot Killed
        
        Idle --> RobotRepairing : Repair Command
        RobotRepairing --> Idle : Repair Complete
        
        Idle --> RobotReloading : Reload Command
        RobotReloading --> Idle : Reload Complete
        
        Idle --> QueryProcessing : Look/State Command
        QueryProcessing --> Idle : Query Response Sent
        
        RobotDestroyed --> Idle : Robot Removed
    }
    
    state Processing {
        [*] --> MessageParsing
        MessageParsing --> InvalidMessage : JSON Error
        MessageParsing --> CommandRouting : Valid JSON
        
        InvalidMessage --> [*] : Error Response
        
        CommandRouting --> RobotCommand : Robot Action
        CommandRouting --> WorldCommand : World Query
        CommandRouting --> UnknownCommand : Invalid Command
        
        RobotCommand --> [*] : Action Executed
        WorldCommand --> [*] : Query Processed
        UnknownCommand --> [*] : Error Response
    }
