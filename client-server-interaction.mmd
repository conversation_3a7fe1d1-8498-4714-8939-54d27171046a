sequenceDiagram
    participant User
    participant CLI
    participant Client
    participant NetworkLayer
    participant Server
    participant CommandProcessor
    participant WorldEngine
    participant World

    User->>CLI: Enter command
    CLI->>CLI: Parse user input
    CLI->>Client: processCommand(command)
    
    Client->>Client: Validate command
    Client->>NetworkLayer: sendMessage(jsonCommand)
    NetworkLayer->>Server: TCP message
    
    Server->>CommandProcessor: parseMessage(json)
    CommandProcessor->>CommandProcessor: Validate JSON
    CommandProcessor->>CommandProcessor: Route command
    
    alt Robot Command
        CommandProcessor->>WorldEngine: executeRobotCommand(command)
        WorldEngine->>World: performAction(robot, action)
        World-->>WorldEngine: ActionResult
        WorldEngine-->>CommandProcessor: CommandResult
        
    else World Command  
        CommandProcessor->>WorldEngine: executeWorldCommand(command)
        WorldEngine->>World: getWorldState()
        World-->>WorldEngine: WorldState
        WorldEngine-->>CommandProcessor: WorldData
    end
    
    CommandProcessor->>Server: formatResponse(result)
    Server->>NetworkLayer: TCP response
    NetworkLayer-->>Client: JSON response
    
    Client->>Client: Process response
    Client->>CLI: updateDisplay(response)
    CLI-->>User: Show result
    
    Note over User, World: This pattern repeats for all commands:<br/>look, move, fire, repair, reload, state, etc.
