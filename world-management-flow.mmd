flowchart TD
    Start([Server Startup]) --> LoadConfig[Load World Configuration]
    LoadConfig --> ValidateConfig{Config Valid?}
    ValidateConfig -->|No| ConfigError[Configuration Error - Exit]
    ValidateConfig -->|Yes| CreateWorld[Create World Instance]
    
    CreateWorld --> SetDimensions[Set World Dimensions]
    SetDimensions --> LoadObstacles[Load Obstacle Definitions]
    LoadObstacles --> ValidateObstacles{Obstacles Valid?}
    ValidateObstacles -->|Overlap| ObstacleError[Obstacle Overlap Error]
    ValidateObstacles -->|Valid| PlaceObstacles[Place Obstacles in World]
    
    PlaceObstacles --> StartServer[Start TCP Server]
    StartServer --> ListenLoop{Listen for Connections}
    
    ListenLoop --> ClientConnect[Client Connection]
    ClientConnect --> <PERSON><PERSON><PERSON>andler[Create Client Handler Thread]
    CreateHandler --> MessageLoop{Message Processing Loop}
    
    MessageLoop --> ReceiveMsg[Receive Client Message]
    ReceiveMsg --> ParseJSON{Valid JSON?}
    ParseJSON -->|No| JSONError[JSON Parse Error]
    ParseJSON -->|Yes| RouteCommand[Route Command]
    
    RouteCommand --> CommandType{Command Type?}
    
    CommandType -->|launch| LaunchRobot[Launch Robot Process]
    CommandType -->|move| MoveRobot[Movement Process]
    CommandType -->|fire| CombatProcess[Combat Process]
    CommandType -->|look| VisionProcess[Vision Process]
    CommandType -->|repair| RepairProcess[Repair Process]
    CommandType -->|reload| ReloadProcess[Reload Process]
    CommandType -->|state| StateQuery[State Query Process]
    CommandType -->|quit| ClientDisconnect[Disconnect Client]
    
    LaunchRobot --> ValidateName{Name Available?}
    ValidateName -->|Taken| NameError[Name Taken Error]
    ValidateName -->|Available| FindSpawn[Find Spawn Position]
    FindSpawn --> SpawnFound{Valid Spawn?}
    SpawnFound -->|No| SpawnError[No Spawn Available]
    SpawnFound -->|Yes| CreateRobotInstance[Create Robot Instance]
    CreateRobotInstance --> AddToWorld[Add Robot to World]
    AddToWorld --> LaunchSuccess[Launch Success Response]
    
    VisionProcess --> GetRobotPos[Get Robot Position]
    GetRobotPos --> CalcVisibility[Calculate Visibility Range]
    CalcVisibility --> ScanDirections[Scan All Directions]
    ScanDirections --> CheckLineOfSight[Check Line of Sight]
    CheckLineOfSight --> BuildVisionResult[Build Vision Result]
    BuildVisionResult --> VisionResponse[Return Vision Response]
    
    RepairProcess --> StartRepair[Start Repair Timer]
    StartRepair --> RepairWait[Wait for Repair Duration]
    RepairWait --> RestoreShield[Restore Shield to Max]
    RestoreShield --> RepairComplete[Repair Complete Response]
    
    ReloadProcess --> StartReload[Start Reload Timer]
    StartReload --> ReloadWait[Wait for Reload Duration]
    ReloadWait --> RestoreAmmo[Restore Ammo to Max]
    RestoreAmmo --> ReloadComplete[Reload Complete Response]
    
    StateQuery --> GetRobotState[Get Current Robot State]
    GetRobotState --> BuildStateResponse[Build State Response]
    BuildStateResponse --> StateResponse[Return State Response]
    
    ClientDisconnect --> RemoveRobots[Remove Client's Robots]
    RemoveRobots --> CloseConnection[Close Connection]
    CloseConnection --> ListenLoop
    
    %% All responses flow back to message loop
    LaunchSuccess --> MessageLoop
    VisionResponse --> MessageLoop
    RepairComplete --> MessageLoop
    ReloadComplete --> MessageLoop
    StateResponse --> MessageLoop
    JSONError --> MessageLoop
    NameError --> MessageLoop
    SpawnError --> MessageLoop
    
    %% Error handling
    ConfigError --> Exit([Server Exit])
    ObstacleError --> Exit
