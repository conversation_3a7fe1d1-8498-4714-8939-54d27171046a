flowchart TD
    Start([Fire Command Received]) --> GetShooter[Get Shooting Robot]
    GetShooter --> ValidateShooter{Robot Valid & Alive?}
    ValidateShooter -->|No| ShooterError[Invalid Shooter Error]
    ValidateShooter -->|Yes| CheckAmmo{Has Ammunition?}
    
    CheckAmmo -->|No| NoAmmoError[No Ammunition Error]
    CheckAmmo -->|Yes| CheckReloading{Currently Reloading?}
    CheckReloading -->|Yes| ReloadingError[Still Reloading Error]
    CheckReloading -->|No| CalculateShot[Calculate Shot Path]
    
    CalculateShot --> GetDirection[Get Robot Direction]
    GetDirection --> GetRange[Get Weapon Range]
    GetRange --> TracePath[Trace Shot Path]
    
    TracePath --> PathStep{For Each Step in Range}
    PathStep --> CheckVision{Vision Blocked?}
    CheckVision -->|Mountain| ShotBlocked[Shot Blocked by Mountain]
    CheckVision -->|Clear| CheckTarget{Target at Position?}
    
    CheckTarget -->|Robot Found| ValidTarget{Valid Target?}
    CheckTarget -->|No Robot| NextPathStep{More Range?}
    
    ValidTarget -->|Self| SelfFireError[Cannot Shoot Self]
    ValidTarget -->|Valid Enemy| HitTarget[Hit Target Robot]
    
    HitTarget --> ReduceAmmo[Reduce Shooter Ammo]
    ReduceAmmo --> CheckShield{Target Has Shield?}
    
    CheckShield -->|No Shield| DirectDamage[Direct Damage - Robot Dies]
    CheckShield -->|Has Shield| DamageShield[Damage Shield]
    
    DamageShield --> ShieldLeft{Shield Strength > 0?}
    ShieldLeft -->|Yes| ShieldAbsorbed[Shield Absorbs Hit]
    ShieldLeft -->|No| ShieldDestroyed[Shield Destroyed - Robot Dies]
    
    DirectDamage --> RemoveTarget[Remove Target from World]
    ShieldDestroyed --> RemoveTarget
    RemoveTarget --> KillResponse[Return Kill Confirmation]
    
    ShieldAbsorbed --> HitResponse[Return Hit Confirmation]
    
    NextPathStep -->|Yes| PathStep
    NextPathStep -->|No| MissedShot[Shot Missed - No Target]
    MissedShot --> ReduceAmmo
    ReduceAmmo --> MissResponse[Return Miss Response]
    
    ShotBlocked --> ReduceAmmo
    ReduceAmmo --> BlockedResponse[Return Blocked Response]
    
    ShooterError --> End([End])
    NoAmmoError --> End
    ReloadingError --> End
    SelfFireError --> End
    KillResponse --> End
    HitResponse --> End
    MissResponse --> End
    BlockedResponse --> End
