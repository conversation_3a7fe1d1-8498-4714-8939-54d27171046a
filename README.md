# Robot World - Complete Design Documentation

This directory contains comprehensive visual design diagrams for the Robot World project, including architecture, user experience, system flows, interactions, and state management diagrams.

## 🚀 Quick Start - View All Diagrams

### **Best Option: Complete HTML Viewer**
Open `all-diagrams.html` in any web browser for the complete interactive documentation with all 14 diagrams organized by category.

```bash
# Open in your default browser
open all-diagrams.html
# OR double-click the file in your file manager
```

## 📊 Complete Diagram Collection

### 🏗️ **Architecture Diagrams**
1. **class-diagram.mmd** - Core classes and relationships
2. **domain-diagram.mmd** - Business domain concepts
3. **client-component-diagram.mmd** - Client application architecture
4. **server-component-diagram.mmd** - Server application architecture

### 👤 **User Experience Diagrams**
5. **user-journey-storyboard.mmd** - Complete user journey storyboard
6. **user-story-flow.mmd** - Detailed user interaction flow

### ⚙️ **System Flow Diagrams**
7. **robot-movement-flow.mmd** - Robot movement processing
8. **combat-resolution-flow.mmd** - Combat system resolution
9. **world-management-flow.mmd** - Server world management

### 🔄 **Interaction Diagrams**
10. **robot-launch-sequence.mmd** - Robot launch sequence
11. **combat-sequence.mmd** - Combat interaction sequence
12. **client-server-interaction.mmd** - General client-server communication

### 📊 **State Management Diagrams**
13. **robot-state-diagram.mmd** - Robot state transitions
14. **world-state-diagram.mmd** - World state management

## 🎯 Viewing Options

### Option 1: Complete HTML Viewer (Recommended)
- **File**: `all-diagrams.html`
- **Features**: All diagrams in one file, navigation, categorized, professional styling
- **Usage**: Open in any web browser, works offline

### Option 2: Individual SVG Files
```bash
# Generate SVG files from Mermaid source
./convert-to-svg.sh
```
- **Benefits**: Scalable, can be embedded in documents, GitLab compatible
- **Usage**: Open directly in browsers, upload to GitLab wiki

### Option 3: Mermaid Live Editor
1. Go to [Mermaid Live Editor](https://mermaid.live/)
2. Copy content from any `.mmd` file
3. Paste and view/export instantly
- **Benefits**: Online editing, export to various formats

### Option 4: VS Code with Mermaid Extension
- Install "Mermaid Markdown Syntax Highlighting" extension
- Open `.mmd` files with live preview
- **Benefits**: Integrated development environment

### Option 5: GitLab Integration
- Upload SVG files to GitLab project wiki
- Reference in wiki: `![Diagram Name](diagram-file.svg)`
- GitLab also supports Mermaid directly in markdown

## 🛠️ Tools and Scripts

- **`generate-complete-html.py`** - Python script to generate the complete HTML viewer
- **`convert-to-svg.sh`** - Bash script to convert all Mermaid files to SVG
- **`all-diagrams.html`** - Complete interactive documentation (generated)

## 📋 Usage for Team Collaboration

1. **Share with team**: Send `all-diagrams.html` file
2. **GitLab wiki**: Upload SVG files and reference in wiki pages
3. **Presentations**: Use SVG files in slides
4. **Documentation**: Embed diagrams in project documentation
5. **Code reviews**: Reference specific diagrams during reviews

## 🔄 Updating Diagrams

1. Edit the `.mmd` source files
2. Run `python3 generate-complete-html.py` to update HTML viewer
3. Run `./convert-to-svg.sh` to update SVG files
4. Commit changes to version control

The diagrams provide a complete visual foundation for understanding, implementing, and maintaining the Robot World system!
