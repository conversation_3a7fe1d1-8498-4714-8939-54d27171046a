# 🔧 Robot World Technical Specifications

## 🏗️ System Architecture Overview

### **High-Level Architecture**
```
┌─────────────────┐    TCP/JSON    ┌─────────────────┐
│   Robot Client  │ ◄─────────────► │  Robot Server   │
│                 │                │                 │
│ • <PERSON><PERSON><PERSON> Interface │                │ • World Engine  │
│ • Visualization │                │ • Multi-client  │
│ • Local State   │                │ • Game Logic    │
└─────────────────┘                └─────────────────┘
```

---

## 📡 Network Protocol Specification

### **Message Format**
All messages use JSON format over TCP connections.

#### **Request Format**
```json
{
  "robot": "robot_name",
  "command": "command_name",
  "arguments": ["arg1", "arg2"]
}
```

#### **Response Format**
```json
{
  "result": "OK" | "ERROR",
  "data": { ... },
  "message": "Human readable message"
}
```

### **Command Categories**

#### **Robot Commands**
- `launch ROBOT_TYPE ROBOT_NAME` - Spawn robot in world
- `move STEPS` - Move robot forward/backward
- `turn left|right` - Turn robot 90 degrees
- `look` - Get visibility information
- `fire` - Fire weapon at target
- `repair` - Repair shields
- `reload` - Reload weapon
- `state` - Get robot status

#### **World Commands**
- `robots` - List all robots in world
- `dump` - Get complete world state
- `quit` - Disconnect from server

### **Error Codes**
- `INVALID_COMMAND` - Unknown command
- `INVALID_ARGUMENTS` - Wrong number/type of arguments
- `ROBOT_NOT_FOUND` - Robot doesn't exist
- `POSITION_BLOCKED` - Movement blocked by obstacle/robot
- `OUT_OF_BOUNDS` - Movement outside world boundaries
- `NO_AMMUNITION` - Weapon needs reloading
- `SHIELD_DESTROYED` - Robot is destroyed

---

## 🎮 Game Mechanics Specification

### **World Properties**
```java
class WorldConfiguration {
    int width = 200;           // World width in units
    int height = 200;          // World height in units
    int visibilityRange = 50;  // Maximum vision distance
    int shieldRepairTime = 5;  // Seconds to repair shield
    int weaponReloadTime = 3;  // Seconds to reload weapon
    int maxShieldStrength = 3; // Maximum shield hits
}
```

### **Robot Types & Specifications**

#### **Sniper Robot**
```json
{
  "type": "sniper",
  "maxShots": 3,
  "weaponRange": 100,
  "shieldStrength": 2,
  "repairTime": 6,
  "reloadTime": 4
}
```

#### **Tank Robot**
```json
{
  "type": "tank", 
  "maxShots": 8,
  "weaponRange": 30,
  "shieldStrength": 5,
  "repairTime": 3,
  "reloadTime": 2
}
```

### **Obstacle Types**

#### **Mountain**
- Blocks movement: ✅
- Blocks vision: ✅
- Destructible: ❌

#### **Lake**
- Blocks movement: ✅
- Blocks vision: ❌
- Destructible: ❌

#### **Bottomless Pit**
- Blocks movement: ✅
- Blocks vision: ❌
- Destroys robot: ✅

### **Combat System**

#### **Hit Detection Algorithm**
```python
def calculate_hit(shooter_pos, target_pos, weapon_range):
    distance = calculate_distance(shooter_pos, target_pos)
    if distance > weapon_range:
        return False
    
    # Check line of sight
    obstacles = get_obstacles_in_line(shooter_pos, target_pos)
    return len(obstacles) == 0
```

#### **Damage Resolution**
1. Check if shot hits target (line of sight + range)
2. If hit, reduce target shield by 1
3. If shield reaches 0, destroy robot
4. Reduce shooter ammunition by 1

---

## 💾 Data Models & Classes

### **Core Domain Classes**

#### **Position Class**
```java
public class Position {
    private final int x;
    private final int y;
    
    public int distanceTo(Position other) {
        return Math.abs(x - other.x) + Math.abs(y - other.y);
    }
    
    public Position move(Direction direction, int steps) {
        // Implementation for movement calculation
    }
}
```

#### **Robot Class**
```java
public class Robot {
    private String name;
    private String make;
    private Position position;
    private Direction direction;
    private Shield shield;
    private Weapon weapon;
    private RobotState state;
    
    public boolean move(int steps) { /* ... */ }
    public void turn(Direction newDirection) { /* ... */ }
    public LookResult look() { /* ... */ }
    public boolean fire() { /* ... */ }
}
```

#### **World Class**
```java
public class World {
    private final int width;
    private final int height;
    private final List<Robot> robots;
    private final List<Obstacle> obstacles;
    
    public boolean addRobot(Robot robot) { /* ... */ }
    public List<Robot> getRobotsInRange(Position pos, int range) { /* ... */ }
    public boolean isValidPosition(Position pos) { /* ... */ }
}
```

### **Network Classes**

#### **Server Architecture**
```java
public class RobotWorldServer {
    private ServerSocket serverSocket;
    private World world;
    private ExecutorService clientHandlers;
    
    public void start(int port) { /* ... */ }
    private void handleClient(Socket clientSocket) { /* ... */ }
}

public class ClientHandler implements Runnable {
    private Socket socket;
    private World world;
    private Robot robot;
    
    public void run() { /* ... */ }
    private void processCommand(String command) { /* ... */ }
}
```

#### **Client Architecture**
```java
public class RobotWorldClient {
    private Socket socket;
    private BufferedReader input;
    private PrintWriter output;
    private WorldView worldView;
    
    public void connect(String host, int port) { /* ... */ }
    public String sendCommand(String command) { /* ... */ }
}
```

---

## 🔄 Game Flow & State Management

### **Server Startup Sequence**
1. Load world configuration from file
2. Create world instance with obstacles
3. Start TCP server on specified port
4. Begin accepting client connections
5. Create client handler threads

### **Client Connection Flow**
1. Connect to server via TCP
2. Send robot launch command
3. Receive spawn confirmation
4. Enter main game loop
5. Process user commands and display results

### **Game Loop (Server Side)**
```python
while server_running:
    for each client_handler:
        if has_pending_command():
            command = receive_command()
            result = process_command(command)
            send_response(result)
    
    update_world_state()
    handle_timed_actions()  # repairs, reloads
    sleep(16)  # ~60 FPS
```

### **Robot State Transitions**
```
NORMAL ──move──► MOVING ──complete──► NORMAL
   │                                     │
   └──repair──► REPAIRING ──complete──► ┘
   │                                     │
   └──reload──► RELOADING ──complete──► ┘
   │                                     │
   └──damage──► DEAD (if shield = 0)
```

---

## 🧪 Testing Strategy

### **Unit Testing Requirements**
- All domain model classes (Position, Robot, World)
- Command parsing and validation
- Combat calculations and hit detection
- Movement validation and collision detection
- Network message serialization/deserialization

### **Integration Testing**
- Client-server communication
- Multi-client scenarios
- Complete game scenarios (spawn, move, combat, death)
- Error handling and recovery
- Performance under load

### **Test Data & Scenarios**
```java
// Example test scenarios
@Test
public void testRobotMovement() {
    Robot robot = new Robot("TestBot", new Position(10, 10));
    boolean moved = robot.move(5);
    assertEquals(new Position(10, 15), robot.getPosition());
}

@Test
public void testCombatHit() {
    Robot shooter = new Robot("Shooter", new Position(0, 0));
    Robot target = new Robot("Target", new Position(10, 0));
    boolean hit = combatEngine.fire(shooter, target);
    assertTrue(hit);
    assertEquals(2, target.getShield().getStrength());
}
```

---

## ⚡ Performance Requirements

### **Server Performance**
- Support 10+ concurrent clients
- Response time < 100ms for commands
- Memory usage < 512MB for full game
- CPU usage < 50% under normal load

### **Client Performance**
- Startup time < 3 seconds
- Command response display < 50ms
- Memory usage < 128MB
- Cross-platform compatibility

### **Network Performance**
- Message size < 1KB average
- Connection timeout: 30 seconds
- Reconnection attempts: 3 with backoff
- Bandwidth usage < 10KB/s per client

---

## 🔒 Security & Error Handling

### **Input Validation**
- Validate all command parameters
- Sanitize robot names (alphanumeric only)
- Limit message size (max 4KB)
- Rate limiting (max 10 commands/second)

### **Error Recovery**
- Graceful handling of client disconnections
- Automatic cleanup of disconnected robots
- Server continues running if client crashes
- Detailed error logging and monitoring

### **Data Integrity**
- Validate world state consistency
- Prevent duplicate robot names
- Ensure position bounds checking
- Atomic operations for state changes

---

## 📚 Implementation Guidelines

### **Code Style Standards**
- Use meaningful variable and method names
- Follow language-specific conventions (Java: camelCase, Python: snake_case)
- Maximum method length: 50 lines
- Comprehensive JavaDoc/docstring comments
- Unit test coverage > 80%

### **Git Workflow**
- Feature branches for each major component
- Pull requests for code review
- Commit messages following conventional format
- Regular integration with main branch

### **Documentation Requirements**
- API documentation for all public methods
- README with setup and usage instructions
- Architecture decision records (ADRs)
- User manual with examples

**This specification provides the foundation for building a robust, scalable Robot World game! 🚀**
