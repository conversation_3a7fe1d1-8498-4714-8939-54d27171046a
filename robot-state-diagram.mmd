stateDiagram-v2
    [*] --> NotLaunched : Robot Created
    
    NotLaunched --> Normal : Launch Command
    
    Normal --> Moving : Move Command
    Moving --> Normal : Movement Complete
    Moving --> Dead : Fall into Pit
    
    Normal --> Repairing : Repair Command
    Repairing --> Normal : Repair Complete
    Repairing --> Dead : Take Fatal Damage
    
    Normal --> Reloading : Reload Command
    Reloading --> Normal : Reload Complete
    Reloading --> Dead : Take Fatal Damage
    
    Normal --> Dead : Take Fatal Damage
    Normal --> Dead : Shield Destroyed
    
    Moving --> Dead : Take Fatal Damage
    
    Dead --> [*] : Remove from World
    
    state Normal {
        [*] --> Ready
        Ready --> Firing : Fire Command
        Firing --> Ready : Shot Complete
        Ready --> Looking : Look Command
        Looking --> Ready : Look Complete
        Ready --> TurningLeft : Turn Left
        Ready --> TurningRight : Turn Right
        TurningLeft --> Ready : Turn Complete
        TurningRight --> Ready : Turn Complete
    }
    
    state Moving {
        [*] --> ValidatingPath
        ValidatingPath --> MovingForward : Path Clear
        ValidatingPath --> MovingBackward : Path Clear
        ValidatingPath --> MovementError : Path Blocked
        MovingForward --> [*] : Destination Reached
        MovingBackward --> [*] : Destination Reached
        MovementError --> [*] : Error Reported
    }
    
    state Repairing {
        [*] --> RepairInProgress
        RepairInProgress --> [*] : Timer Complete
        note right of RepairInProgress : Cannot move or fire\nwhile repairing
    }
    
    state Reloading {
        [*] --> ReloadInProgress
        ReloadInProgress --> [*] : Timer Complete
        note right of ReloadInProgress : Cannot move or fire\nwhile reloading
    }
