#!/bin/bash

# Script to convert Mermaid diagrams to SVG files
# Requires: npm install -g @mermaid-js/mermaid-cli

echo "Converting Mermaid diagrams to SVG..."

# Check if mmdc is installed
if ! command -v mmdc &> /dev/null; then
    echo "Error: mermaid-cli not found!"
    echo "Please install it with: npm install -g @mermaid-js/mermaid-cli"
    exit 1
fi

# Convert each diagram
echo "Converting class diagram..."
mmdc -i class-diagram.mmd -o class-diagram.svg -t default -b white

echo "Converting domain diagram..."
mmdc -i domain-diagram.mmd -o domain-diagram.svg -t default -b white

echo "Converting client component diagram..."
mmdc -i client-component-diagram.mmd -o client-component-diagram.svg -t default -b white

echo "Converting server component diagram..."
mmdc -i server-component-diagram.mmd -o server-component-diagram.svg -t default -b white

echo "All diagrams converted to SVG!"
echo ""
echo "Files created:"
echo "- class-diagram.svg"
echo "- domain-diagram.svg" 
echo "- client-component-diagram.svg"
echo "- server-component-diagram.svg"
echo ""
echo "You can now:"
echo "1. Open SVG files directly in any web browser"
echo "2. Upload them to GitLab wiki or issues"
echo "3. Share them via email or messaging"
echo "4. Include them in presentations"
