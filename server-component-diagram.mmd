graph TB
    subgraph "Server Application"
        subgraph "Network Layer"
            ServerSocket[Server Socket<br/>TCP listener<br/>Client connections]
            ClientHandler[Client Handler<br/>Per-client threads<br/>Connection management]
            MessageRouter[Message Router<br/>Request routing<br/>Response handling]
        end

        subgraph "Command Processing Layer"
            CommandParser[Command Parser<br/>JSON parsing<br/>Command validation]
            CommandDispatcher[Command Dispatcher<br/>Command routing<br/>Handler selection]
            WorldCommandHandler[World Command Handler<br/>quit, robots, dump<br/>World management]
            RobotCommandHandler[Robot Command Handler<br/>Robot actions<br/>Movement, combat]
        end

        subgraph "Game Engine Layer"
            WorldEngine[World Engine<br/>Game state management<br/>Rule enforcement]
            MovementEngine[Movement Engine<br/>Position validation<br/>Collision detection]
            CombatEngine[Combat Engine<br/>Weapon firing<br/>Damage resolution]
            VisibilityEngine[Visibility Engine<br/>Line of sight<br/>Range calculations]
        end

        subgraph "Domain Layer"
            WorldModel[World Model<br/>Grid state<br/>Robot & obstacle tracking]
            RobotManager[Robot Manager<br/>Robot lifecycle<br/>State management]
            ObstacleManager[Obstacle Manager<br/>Obstacle definitions<br/>Collision queries]
        end

        subgraph "Configuration Layer"
            ConfigManager[Configuration Manager<br/>World settings<br/>Parameter loading]
            WorldFactory[World Factory<br/>World initialization<br/>Obstacle placement]
        end

        subgraph "Persistence Layer"
            StateManager[State Manager<br/>Game state persistence<br/>Session management]
            LogManager[Log Manager<br/>Action logging<br/>Audit trail]
        end
    end

    subgraph "External Systems"
        ConfigFiles[Configuration Files<br/>World parameters<br/>Obstacle definitions]
        Clients[Robot Clients<br/>TCP connections<br/>JSON messages]
        Console[Server Console<br/>Admin commands<br/>World management]
    end

    %% Component Relationships
    Clients --> ServerSocket
    ServerSocket --> ClientHandler
    ClientHandler --> MessageRouter
    MessageRouter --> CommandParser

    CommandParser --> CommandDispatcher
    CommandDispatcher --> WorldCommandHandler
    CommandDispatcher --> RobotCommandHandler

    WorldCommandHandler --> WorldEngine
    RobotCommandHandler --> WorldEngine
    RobotCommandHandler --> MovementEngine
    RobotCommandHandler --> CombatEngine
    RobotCommandHandler --> VisibilityEngine

    WorldEngine --> WorldModel
    WorldEngine --> RobotManager
    WorldEngine --> ObstacleManager

    MovementEngine --> WorldModel
    MovementEngine --> ObstacleManager
    CombatEngine --> RobotManager
    VisibilityEngine --> WorldModel
    VisibilityEngine --> ObstacleManager

    ConfigManager --> ConfigFiles
    ConfigManager --> WorldFactory
    WorldFactory --> WorldModel
    WorldFactory --> ObstacleManager

    StateManager --> WorldModel
    StateManager --> RobotManager
    LogManager --> WorldEngine

    Console --> WorldCommandHandler

    %% Data Flow
    MessageRouter -.->|"JSON responses"| ClientHandler
    WorldModel -.->|"world state"| WorldCommandHandler
    RobotManager -.->|"robot states"| RobotCommandHandler
