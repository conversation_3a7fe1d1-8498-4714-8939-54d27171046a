journey
    title Robot World - User Journey Storyboard
    section Setup & Launch
      Start Client: 5: User
      Connect to Server: 4: User, Client
      Choose Robot Make: 5: User
      Launch Robot: 5: User, Client, Server
      Robot Spawns: 4: Server, World
    section Exploration
      Look Around: 5: User, Robot
      See World State: 4: Robot, World
      Plan Movement: 5: User
      Move Forward: 4: User, <PERSON>, World
      Encounter Obstacle: 3: Robot, World
      Navigate Around: 4: User, Robot
    section Combat Preparation  
      Spot Enemy Robot: 3: Robot, World
      Check Weapon Status: 4: User, Robot
      Position for Attack: 4: User, Robot
      Reload if Needed: 3: User, Robot
    section Combat
      Fire at Enemy: 2: User, Robot, World
      Enemy Takes Damage: 2: Enemy, World
      Take Return Fire: 1: Robot, Enemy
      Shield Absorbs Hit: 3: Robot, Shield
      Repair Shields: 3: User, Robot
    section Victory/Defeat
      Enemy Destroyed: 5: Robot, World
      Celebrate Victory: 5: User
      Continue Exploring: 4: User, Robot
      OR Get Destroyed: 1: Robot, World
      Respawn Decision: 3: User
