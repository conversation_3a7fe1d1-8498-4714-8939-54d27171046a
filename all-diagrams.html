<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robot World - Complete Design Documentation</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            line-height: 1.6;
        }
        .diagram-container {
            background: white;
            margin: 30px 0;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #007acc;
        }
        .nav-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            position: sticky;
            top: 20px;
            z-index: 100;
        }
        .nav-links {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }
        .nav-link {
            background: #007acc;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.3s;
        }
        .nav-link:hover {
            background: #005a9e;
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #007acc;
            border-bottom: 2px solid #ddd;
            padding-bottom: 8px;
            margin-top: 0;
            font-size: 1.8em;
        }
        .description {
            color: #666;
            font-style: italic;
            margin-bottom: 20px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid #007acc;
        }
        .mermaid {
            text-align: center;
            background: #fafbfc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .export-buttons {
            text-align: center;
            margin: 15px 0;
        }
        .export-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 8px;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background 0.3s;
        }
        .export-btn:hover {
            background: #218838;
        }
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 40px 0 20px 0;
            text-align: center;
        }
        .category-header h2 {
            margin: 0;
            border: none;
            color: white;
        }
        .toc {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin-top: 0;
            color: #495057;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #007acc;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            display: block;
            transition: background 0.3s;
        }
        .toc a:hover {
            background: #007acc;
            color: white;
        }

        /* Smooth scrolling for all anchor links */
        html {
            scroll-behavior: smooth;
        }

        /* Offset for sticky navigation */
        .diagram-container, .category-header {
            scroll-margin-top: 120px;
        }

        /* Highlight active navigation links */
        .nav-link.active {
            background: #005a9e;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toc a.active {
            background: #007acc;
            color: white;
        }

        /* Zoom functionality styles */
        .diagram-wrapper {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            background: #fafbfc;
            cursor: grab;
        }

        .diagram-wrapper:active {
            cursor: grabbing;
        }

        .diagram-wrapper.zoomed {
            overflow: auto;
            max-height: 600px;
        }

        .mermaid {
            transition: transform 0.3s ease;
            transform-origin: center center;
        }

        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .diagram-wrapper:hover .zoom-controls {
            opacity: 1;
        }

        .zoom-btn {
            background: rgba(0, 122, 204, 0.9);
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .zoom-btn:hover {
            background: rgba(0, 90, 158, 0.9);
            transform: scale(1.1);
        }

        .zoom-btn:active {
            transform: scale(0.95);
        }

        .fullscreen-btn {
            background: rgba(40, 167, 69, 0.9);
        }

        .fullscreen-btn:hover {
            background: rgba(33, 136, 56, 0.9);
        }

        .reset-btn {
            background: rgba(108, 117, 125, 0.9);
        }

        .reset-btn:hover {
            background: rgba(90, 98, 104, 0.9);
        }

        /* Fullscreen modal styles */
        .fullscreen-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            overflow: auto;
        }

        .fullscreen-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fullscreen-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 95%;
            max-height: 95%;
            overflow: auto;
            position: relative;
        }

        .fullscreen-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #dc3545;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            z-index: 1001;
        }

        .fullscreen-close:hover {
            background: #c82333;
        }

        /* Zoom indicator */
        .zoom-indicator {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .diagram-wrapper:hover .zoom-indicator {
            opacity: 1;
        }
    </style>
</head>
<body>
    <h1>🤖 Robot World - Complete Design Documentation</h1>
    
    <div class="nav-container">
        <div class="nav-links">
            <a href="#architecture" class="nav-link">🏗️ Architecture</a>
            <a href="#user-experience" class="nav-link">👤 User Experience</a>
            <a href="#system-flows" class="nav-link">⚙️ System Flows</a>
            <a href="#interactions" class="nav-link">🔄 Interactions</a>
            <a href="#state-management" class="nav-link">📊 State Management</a>
        </div>
    </div>

    <div class="toc">
        <h3>📋 Complete Documentation Index</h3>
        <ul>
            <li><a href="#class-diagram">🏗️ Class Diagram</a></li>
            <li><a href="#domain-diagram">🌐 Domain Diagram</a></li>
            <li><a href="#client-component-diagram">💻 Client Component Diagram</a></li>
            <li><a href="#server-component-diagram">🖥️ Server Component Diagram</a></li>
            <li><a href="#user-journey-storyboard">📖 User Journey Storyboard</a></li>
            <li><a href="#user-story-flow">🎯 User Story Flow</a></li>
            <li><a href="#robot-movement-flow">🚶 Robot Movement Flow</a></li>
            <li><a href="#combat-resolution-flow">⚔️ Combat Resolution Flow</a></li>
            <li><a href="#world-management-flow">🌍 World Management Flow</a></li>
            <li><a href="#robot-launch-sequence">🚀 Robot Launch Sequence</a></li>
            <li><a href="#combat-sequence">💥 Combat Sequence</a></li>
            <li><a href="#client-server-interaction">🔄 Client-Server Interaction</a></li>
            <li><a href="#robot-state-diagram">🤖 Robot State Diagram</a></li>
            <li><a href="#world-state-diagram">🗺️ World State Diagram</a></li>
        </ul>
    </div>

    <!-- Fullscreen Modal -->
    <div class="fullscreen-modal" id="fullscreen-modal">
        <div class="fullscreen-content">
            <button class="fullscreen-close" onclick="closeFullscreen()">×</button>
            <div id="fullscreen-diagram"></div>
        </div>
    </div>

    <!-- 🏗️ SYSTEM ARCHITECTURE -->
    <div class="category-header" id="architecture">
        <h2>🏗️ System Architecture</h2>
        <p>Core structural diagrams showing the system architecture and design</p>
    </div>

    <div class="diagram-container" id="class-diagram">
        <h2>🏗️ Class Diagram</h2>
        <div class="description">Shows the most important classes and their relationships in the Robot World system. This diagram defines the core object-oriented structure including World, Robot, Obstacles, Shield, Weapon, and their interactions.</div>
        <div class="export-buttons">
            <a href="class-diagram.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="class-diagram">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-class-diagram">
classDiagram
    class World {
        -int width
        -int height
        -int visibilityRange
        -List~Robot~ robots
        -List~Obstacle~ obstacles
        -WorldConfiguration config
        +addRobot(Robot robot) boolean
        +removeRobot(String name) boolean
        +getRobots() List~Robot~
        +getObstacles() List~Obstacle~
        +isValidPosition(Position pos) boolean
        +isObstructed(Position pos) boolean
        +getRobotsInRange(Position pos, int range) List~Robot~
        +dump() String
    }

    class Robot {
        -String name
        -String make
        -Position position
        -Direction direction
        -Shield shield
        -Weapon weapon
        -RobotState state
        +move(int steps) boolean
        +turn(Direction dir) void
        +look() LookResult
        +fire() boolean
        +repair() void
        +reload() void
        +getState() RobotState
        +takeDamage() boolean
        +isAlive() boolean
    }

    class Position {
        -int x
        -int y
        +getX() int
        +getY() int
        +distanceTo(Position other) int
        +equals(Object obj) boolean
    }

    class Direction {
        <<enumeration>>
        NORTH
        SOUTH
        EAST
        WEST
        +turnLeft() Direction
        +turnRight() Direction
        +opposite() Direction
    }

    class Shield {
        -int currentStrength
        -int maxStrength
        -boolean repairing
        +takeDamage() boolean
        +repair(int repairTime) void
        +isDestroyed() boolean
        +getStrength() int
    }

    class Weapon {
        -int shots
        -int maxShots
        -int range
        -boolean reloading
        +fire() boolean
        +reload(int reloadTime) void
        +canFire() boolean
        +getShots() int
    }

    class Obstacle {
        <<abstract>>
        -Position topLeft
        -Position bottomRight
        +contains(Position pos) boolean
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Mountain {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Lake {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class BottomlessPit {
        +blocksMovement() boolean
        +blocksVision() boolean
        +destroysRobot() boolean
    }

    class WorldConfiguration {
        -int worldWidth
        -int worldHeight
        -int visibilityRange
        -int shieldRepairTime
        -int weaponReloadTime
        -int maxShieldStrength
        +loadFromFile(String filename) WorldConfiguration
    }

    class RobotState {
        -Position position
        -Direction direction
        -int shieldStrength
        -int shots
        -String status
    }

    class LookResult {
        -List~Obstacle~ obstacles
        -List~Robot~ robots
        -List~String~ edges
    }

    %% Relationships
    World "1" --> "*" Robot : contains
    World "1" --> "*" Obstacle : contains
    World "1" --> "1" WorldConfiguration : uses
    Robot "1" --> "1" Position : has
    Robot "1" --> "1" Direction : faces
    Robot "1" --> "1" Shield : has
    Robot "1" --> "1" Weapon : has
    Robot "1" --> "1" RobotState : provides
    Obstacle <|-- Mountain : extends
    Obstacle <|-- Lake : extends
    Obstacle <|-- BottomlessPit : extends
    Obstacle "1" --> "2" Position : defined by
    Robot --> LookResult : creates

            </div>
        </div>
    </div>

    <div class="diagram-container" id="domain-diagram">
        <h2>🌐 Domain Diagram</h2>
        <div class="description">Characterizes the domain classes and how they work together, showing business concepts and their interactions across different domains like Game World, Robot, Combat, Obstacles, Configuration, and Communication.</div>
        <div class="export-buttons">
            <a href="domain-diagram.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="domain-diagram">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-domain-diagram">
graph TB
    subgraph "Game World Domain"
        World[World<br/>Grid-based battlefield<br/>Configurable size]
        Grid[Grid System<br/>Coordinate system<br/>Position tracking]
        Visibility[Visibility System<br/>Line of sight<br/>Range limitations]
    end

    subgraph "Robot Domain"
        Robot[Robot<br/>Player entity<br/>Named & typed]
        Movement[Movement System<br/>Forward/back/turn<br/>Obstacle avoidance]
        Orientation[Orientation<br/>N/S/E/W facing<br/>Direction tracking]
    end

    subgraph "Combat Domain"
        Weapon[Weapon System<br/>Shots & range<br/>Reload mechanics]
        Shield[Shield System<br/>Damage absorption<br/>Repair mechanics]
        Combat[Combat Resolution<br/>Hit detection<br/>Damage calculation]
    end

    subgraph "Obstacle Domain"
        Mountain[Mountain<br/>Blocks movement<br/>Blocks vision]
        Lake[Lake<br/>Blocks movement<br/>Allows vision]
        Pit[Bottomless Pit<br/>Destroys robots<br/>Allows vision]
    end

    subgraph "Configuration Domain"
        WorldConfig[World Configuration<br/>Size, visibility<br/>Timing parameters]
        RobotMakes[Robot Makes<br/>Different configurations<br/>Sniper, Tank, etc.]
    end

    subgraph "Communication Domain"
        Protocol[TCP/IP Protocol<br/>JSON messages<br/>Request-response]
        Commands[Command System<br/>Robot actions<br/>World queries]
    end

    %% Domain Relationships
    World --> Grid
    World --> Visibility
    World --> Mountain
    World --> Lake
    World --> Pit
    World --> Robot

    Robot --> Movement
    Robot --> Orientation
    Robot --> Weapon
    Robot --> Shield
    Robot --> Combat

    Movement --> Grid
    Movement --> Mountain
    Movement --> Lake
    Movement --> Pit

    Visibility --> Grid
    Visibility --> Mountain
    
    Combat --> Weapon
    Combat --> Shield
    Combat --> Robot

    WorldConfig --> World
    WorldConfig --> Visibility
    WorldConfig --> Shield
    WorldConfig --> Weapon

    RobotMakes --> Robot
    RobotMakes --> Weapon
    RobotMakes --> Shield

    Protocol --> Commands
    Commands --> Robot
    Commands --> World

    %% Key interactions
    Robot -.->|"looks around"| Visibility
    Robot -.->|"fires at"| Robot
    Robot -.->|"moves through"| Grid
    Weapon -.->|"limited by"| Visibility
    Shield -.->|"protects from"| Combat

            </div>
        </div>
    </div>

    <div class="diagram-container" id="client-component-diagram">
        <h2>💻 Client Component Diagram</h2>
        <div class="description">Shows the architecture and components of the robot client application, including User Interface, Application, Domain, Communication, and Configuration layers.</div>
        <div class="export-buttons">
            <a href="client-component-diagram.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="client-component-diagram">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-client-component-diagram">
graph TB
    subgraph "Client Application"
        subgraph "User Interface Layer"
            CLI[Command Line Interface<br/>User input/output<br/>Command parsing]
            Display[Display Manager<br/>World visualization<br/>Status reporting]
        end

        subgraph "Application Layer"
            ClientApp[Client Application<br/>Main controller<br/>Command coordination]
            RobotController[Robot Controller<br/>Robot state management<br/>Action execution]
            CommandProcessor[Command Processor<br/>Input validation<br/>Command routing]
        end

        subgraph "Domain Layer"
            RobotModel[Robot Model<br/>Local robot state<br/>Position & status]
            WorldView[World View<br/>Local world knowledge<br/>Obstacle & robot tracking]
            RobotMake[Robot Make<br/>Configuration<br/>Capabilities]
        end

        subgraph "Communication Layer"
            NetworkClient[Network Client<br/>TCP connection<br/>Message handling]
            MessageSerializer[Message Serializer<br/>JSON encoding/decoding<br/>Protocol compliance]
            ConnectionManager[Connection Manager<br/>Connection lifecycle<br/>Error handling]
        end

        subgraph "Configuration Layer"
            ConfigLoader[Configuration Loader<br/>Robot make definitions<br/>Client settings]
            RobotFactory[Robot Factory<br/>Robot instantiation<br/>Make-specific setup]
        end
    end

    subgraph "External Systems"
        Server[Robot World Server<br/>TCP/IP endpoint]
        ConfigFiles[Configuration Files<br/>Robot makes<br/>Client settings]
        User[User<br/>Command input<br/>Visual feedback]
    end

    %% Component Relationships
    User --> CLI
    CLI --> CommandProcessor
    CommandProcessor --> ClientApp
    ClientApp --> RobotController
    ClientApp --> Display

    RobotController --> RobotModel
    RobotController --> WorldView
    RobotController --> NetworkClient

    NetworkClient --> MessageSerializer
    NetworkClient --> ConnectionManager
    ConnectionManager --> Server

    ConfigLoader --> ConfigFiles
    ConfigLoader --> RobotFactory
    RobotFactory --> RobotMake
    RobotFactory --> RobotModel

    Display --> WorldView
    Display --> RobotModel

    %% Data Flow
    MessageSerializer -.->|"JSON messages"| Server
    Server -.->|"JSON responses"| MessageSerializer
    WorldView -.->|"world state"| Display
    RobotModel -.->|"robot status"| Display

            </div>
        </div>
    </div>

    <div class="diagram-container" id="server-component-diagram">
        <h2>🖥️ Server Component Diagram</h2>
        <div class="description">Shows the server architecture for the portion that reads and executes commands, including Network, Command Processing, Game Engine, Domain, Configuration, and Persistence layers.</div>
        <div class="export-buttons">
            <a href="server-component-diagram.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="server-component-diagram">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-server-component-diagram">
graph TB
    subgraph "Server Application"
        subgraph "Network Layer"
            ServerSocket[Server Socket<br/>TCP listener<br/>Client connections]
            ClientHandler[Client Handler<br/>Per-client threads<br/>Connection management]
            MessageRouter[Message Router<br/>Request routing<br/>Response handling]
        end

        subgraph "Command Processing Layer"
            CommandParser[Command Parser<br/>JSON parsing<br/>Command validation]
            CommandDispatcher[Command Dispatcher<br/>Command routing<br/>Handler selection]
            WorldCommandHandler[World Command Handler<br/>quit, robots, dump<br/>World management]
            RobotCommandHandler[Robot Command Handler<br/>Robot actions<br/>Movement, combat]
        end

        subgraph "Game Engine Layer"
            WorldEngine[World Engine<br/>Game state management<br/>Rule enforcement]
            MovementEngine[Movement Engine<br/>Position validation<br/>Collision detection]
            CombatEngine[Combat Engine<br/>Weapon firing<br/>Damage resolution]
            VisibilityEngine[Visibility Engine<br/>Line of sight<br/>Range calculations]
        end

        subgraph "Domain Layer"
            WorldModel[World Model<br/>Grid state<br/>Robot & obstacle tracking]
            RobotManager[Robot Manager<br/>Robot lifecycle<br/>State management]
            ObstacleManager[Obstacle Manager<br/>Obstacle definitions<br/>Collision queries]
        end

        subgraph "Configuration Layer"
            ConfigManager[Configuration Manager<br/>World settings<br/>Parameter loading]
            WorldFactory[World Factory<br/>World initialization<br/>Obstacle placement]
        end

        subgraph "Persistence Layer"
            StateManager[State Manager<br/>Game state persistence<br/>Session management]
            LogManager[Log Manager<br/>Action logging<br/>Audit trail]
        end
    end

    subgraph "External Systems"
        ConfigFiles[Configuration Files<br/>World parameters<br/>Obstacle definitions]
        Clients[Robot Clients<br/>TCP connections<br/>JSON messages]
        Console[Server Console<br/>Admin commands<br/>World management]
    end

    %% Component Relationships
    Clients --> ServerSocket
    ServerSocket --> ClientHandler
    ClientHandler --> MessageRouter
    MessageRouter --> CommandParser

    CommandParser --> CommandDispatcher
    CommandDispatcher --> WorldCommandHandler
    CommandDispatcher --> RobotCommandHandler

    WorldCommandHandler --> WorldEngine
    RobotCommandHandler --> WorldEngine
    RobotCommandHandler --> MovementEngine
    RobotCommandHandler --> CombatEngine
    RobotCommandHandler --> VisibilityEngine

    WorldEngine --> WorldModel
    WorldEngine --> RobotManager
    WorldEngine --> ObstacleManager

    MovementEngine --> WorldModel
    MovementEngine --> ObstacleManager
    CombatEngine --> RobotManager
    VisibilityEngine --> WorldModel
    VisibilityEngine --> ObstacleManager

    ConfigManager --> ConfigFiles
    ConfigManager --> WorldFactory
    WorldFactory --> WorldModel
    WorldFactory --> ObstacleManager

    StateManager --> WorldModel
    StateManager --> RobotManager
    LogManager --> WorldEngine

    Console --> WorldCommandHandler

    %% Data Flow
    MessageRouter -.->|"JSON responses"| ClientHandler
    WorldModel -.->|"world state"| WorldCommandHandler
    RobotManager -.->|"robot states"| RobotCommandHandler

            </div>
        </div>
    </div>

    <!-- 👤 USER EXPERIENCE -->
    <div class="category-header" id="user-experience">
        <h2>👤 User Experience</h2>
        <p>User journey and interaction flow diagrams</p>
    </div>

    <div class="diagram-container" id="user-journey-storyboard">
        <h2>📖 User Journey Storyboard</h2>
        <div class="description">Visual storyboard showing the complete user experience from setup and launch through exploration, combat preparation, combat, and victory/defeat scenarios.</div>
        <div class="export-buttons">
            <a href="user-journey-storyboard.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="user-journey-storyboard">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-user-journey-storyboard">
journey
    title Robot World - User Journey Storyboard
    section Setup & Launch
      Start Client: 5: User
      Connect to Server: 4: User, Client
      Choose Robot Make: 5: User
      Launch Robot: 5: User, Client, Server
      Robot Spawns: 4: Server, World
    section Exploration
      Look Around: 5: User, Robot
      See World State: 4: Robot, World
      Plan Movement: 5: User
      Move Forward: 4: User, Robot, World
      Encounter Obstacle: 3: Robot, World
      Navigate Around: 4: User, Robot
    section Combat Preparation  
      Spot Enemy Robot: 3: Robot, World
      Check Weapon Status: 4: User, Robot
      Position for Attack: 4: User, Robot
      Reload if Needed: 3: User, Robot
    section Combat
      Fire at Enemy: 2: User, Robot, World
      Enemy Takes Damage: 2: Enemy, World
      Take Return Fire: 1: Robot, Enemy
      Shield Absorbs Hit: 3: Robot, Shield
      Repair Shields: 3: User, Robot
    section Victory/Defeat
      Enemy Destroyed: 5: Robot, World
      Celebrate Victory: 5: User
      Continue Exploring: 4: User, Robot
      OR Get Destroyed: 1: Robot, World
      Respawn Decision: 3: User

            </div>
        </div>
    </div>

    <div class="diagram-container" id="user-story-flow">
        <h2>🎯 User Story Flow</h2>
        <div class="description">Detailed flowchart showing the complete user interaction flow from starting the client through all possible game scenarios and decision points.</div>
        <div class="export-buttons">
            <a href="user-story-flow.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="user-story-flow">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-user-story-flow">
flowchart TD
    Start([User Starts Client]) --> Connect{Connect to Server?}
    Connect -->|Success| ChooseMake[Choose Robot Make<br/>Sniper, Tank, etc.]
    Connect -->|Failed| Error1[Show Connection Error]
    Error1 --> Start
    
    ChooseMake --> EnterName[Enter Robot Name]
    EnterName --> Launch[Launch Robot Command]
    Launch --> ServerCheck{Server Validates?}
    
    ServerCheck -->|Name Taken| Error2[Name Already Exists]
    ServerCheck -->|Success| Spawn[Robot Spawns in World]
    Error2 --> EnterName
    
    Spawn --> MainLoop{Main Game Loop}
    
    MainLoop --> Look[Look Around]
    Look --> ShowWorld[Display World State<br/>- Obstacles<br/>- Other Robots<br/>- Edges]
    
    MainLoop --> Move[Move Robot]
    Move --> ValidateMove{Valid Move?}
    ValidateMove -->|Blocked| ShowError[Show Movement Error]
    ValidateMove -->|Success| UpdatePos[Update Position]
    UpdatePos --> MainLoop
    ShowError --> MainLoop
    
    MainLoop --> Combat[Combat Actions]
    Combat --> Fire{Fire Weapon?}
    Fire -->|No Ammo| NeedReload[Need to Reload]
    Fire -->|Success| CheckHit{Hit Target?}
    CheckHit -->|Hit| DamageTarget[Target Takes Damage]
    CheckHit -->|Miss| MainLoop
    DamageTarget --> TargetDead{Target Destroyed?}
    TargetDead -->|Yes| Victory[Victory Message]
    TargetDead -->|No| MainLoop
    Victory --> MainLoop
    
    NeedReload --> Reload[Reload Weapon]
    Reload --> WaitReload[Wait for Reload Time]
    WaitReload --> MainLoop
    
    MainLoop --> Repair[Repair Shields]
    Repair --> WaitRepair[Wait for Repair Time]
    WaitRepair --> MainLoop
    
    MainLoop --> TakeDamage{Take Damage?}
    TakeDamage -->|Shield Absorbs| MainLoop
    TakeDamage -->|Robot Destroyed| Death[Robot Dies]
    Death --> GameOver[Game Over]
    GameOver --> Restart{Play Again?}
    Restart -->|Yes| Start
    Restart -->|No| Exit([Exit Game])
    
    MainLoop --> Quit[Quit Command]
    Quit --> Exit

            </div>
        </div>
    </div>

    <!-- ⚙️ SYSTEM FLOWS -->
    <div class="category-header" id="system-flows">
        <h2>⚙️ System Flows</h2>
        <p>Detailed system process flow diagrams</p>
    </div>

    <div class="diagram-container" id="robot-movement-flow">
        <h2>🚶 Robot Movement Flow</h2>
        <div class="description">System flow diagram showing how robot movement commands are processed, including validation, path checking, obstacle detection, and collision handling.</div>
        <div class="export-buttons">
            <a href="robot-movement-flow.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="robot-movement-flow">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-robot-movement-flow">
flowchart TD
    Start([Movement Command Received]) --> Parse[Parse Command<br/>forward/back/turn]
    Parse --> ValidateCmd{Valid Command?}
    ValidateCmd -->|No| ErrorResp[Return Error Response]
    ValidateCmd -->|Yes| GetRobot[Get Robot from World]
    
    GetRobot --> RobotExists{Robot Exists?}
    RobotExists -->|No| NotFound[Robot Not Found Error]
    RobotExists -->|Yes| CheckState{Robot Alive?}
    
    CheckState -->|Dead| DeadError[Robot is Dead Error]
    CheckState -->|Repairing| BusyError[Robot is Repairing Error]
    CheckState -->|Reloading| BusyError
    CheckState -->|Ready| ProcessMove[Process Movement]
    
    ProcessMove --> MoveType{Movement Type?}
    
    MoveType -->|Turn| UpdateDirection[Update Robot Direction]
    UpdateDirection --> Success[Return Success Response]
    
    MoveType -->|Forward/Back| CalcNewPos[Calculate New Position]
    CalcNewPos --> ValidateSteps{Valid Step Count?}
    ValidateSteps -->|No| InvalidSteps[Invalid Steps Error]
    ValidateSteps -->|Yes| CheckPath[Check Movement Path]
    
    CheckPath --> PathLoop{For Each Step}
    PathLoop --> CheckBounds{Within World Bounds?}
    CheckBounds -->|No| EdgeError[Hit World Edge Error]
    CheckBounds -->|Yes| CheckObstacle{Obstacle in Path?}
    
    CheckObstacle -->|Mountain/Lake| BlockedError[Path Blocked Error]
    CheckObstacle -->|Bottomless Pit| FallIntoPit[Robot Falls & Dies]
    CheckObstacle -->|None| CheckRobot{Other Robot Here?}
    
    CheckRobot -->|Yes| CollisionError[Robot Collision Error]
    CheckRobot -->|No| NextStep{More Steps?}
    
    NextStep -->|Yes| PathLoop
    NextStep -->|No| UpdatePosition[Update Robot Position]
    UpdatePosition --> Success
    
    FallIntoPit --> RemoveRobot[Remove Robot from World]
    RemoveRobot --> DeathResp[Return Death Response]
    
    ErrorResp --> End([End])
    NotFound --> End
    DeadError --> End
    BusyError --> End
    InvalidSteps --> End
    EdgeError --> End
    BlockedError --> End
    CollisionError --> End
    Success --> End
    DeathResp --> End

            </div>
        </div>
    </div>

    <div class="diagram-container" id="combat-resolution-flow">
        <h2>⚔️ Combat Resolution Flow</h2>
        <div class="description">Detailed flow showing how combat is resolved, including ammunition checks, shot tracing, hit detection, shield damage, and robot destruction.</div>
        <div class="export-buttons">
            <a href="combat-resolution-flow.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="combat-resolution-flow">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-combat-resolution-flow">
flowchart TD
    Start([Fire Command Received]) --> GetShooter[Get Shooting Robot]
    GetShooter --> ValidateShooter{Robot Valid & Alive?}
    ValidateShooter -->|No| ShooterError[Invalid Shooter Error]
    ValidateShooter -->|Yes| CheckAmmo{Has Ammunition?}
    
    CheckAmmo -->|No| NoAmmoError[No Ammunition Error]
    CheckAmmo -->|Yes| CheckReloading{Currently Reloading?}
    CheckReloading -->|Yes| ReloadingError[Still Reloading Error]
    CheckReloading -->|No| CalculateShot[Calculate Shot Path]
    
    CalculateShot --> GetDirection[Get Robot Direction]
    GetDirection --> GetRange[Get Weapon Range]
    GetRange --> TracePath[Trace Shot Path]
    
    TracePath --> PathStep{For Each Step in Range}
    PathStep --> CheckVision{Vision Blocked?}
    CheckVision -->|Mountain| ShotBlocked[Shot Blocked by Mountain]
    CheckVision -->|Clear| CheckTarget{Target at Position?}
    
    CheckTarget -->|Robot Found| ValidTarget{Valid Target?}
    CheckTarget -->|No Robot| NextPathStep{More Range?}
    
    ValidTarget -->|Self| SelfFireError[Cannot Shoot Self]
    ValidTarget -->|Valid Enemy| HitTarget[Hit Target Robot]
    
    HitTarget --> ReduceAmmo[Reduce Shooter Ammo]
    ReduceAmmo --> CheckShield{Target Has Shield?}
    
    CheckShield -->|No Shield| DirectDamage[Direct Damage - Robot Dies]
    CheckShield -->|Has Shield| DamageShield[Damage Shield]
    
    DamageShield --> ShieldLeft{Shield Strength > 0?}
    ShieldLeft -->|Yes| ShieldAbsorbed[Shield Absorbs Hit]
    ShieldLeft -->|No| ShieldDestroyed[Shield Destroyed - Robot Dies]
    
    DirectDamage --> RemoveTarget[Remove Target from World]
    ShieldDestroyed --> RemoveTarget
    RemoveTarget --> KillResponse[Return Kill Confirmation]
    
    ShieldAbsorbed --> HitResponse[Return Hit Confirmation]
    
    NextPathStep -->|Yes| PathStep
    NextPathStep -->|No| MissedShot[Shot Missed - No Target]
    MissedShot --> ReduceAmmo
    ReduceAmmo --> MissResponse[Return Miss Response]
    
    ShotBlocked --> ReduceAmmo
    ReduceAmmo --> BlockedResponse[Return Blocked Response]
    
    ShooterError --> End([End])
    NoAmmoError --> End
    ReloadingError --> End
    SelfFireError --> End
    KillResponse --> End
    HitResponse --> End
    MissResponse --> End
    BlockedResponse --> End

            </div>
        </div>
    </div>

    <div class="diagram-container" id="world-management-flow">
        <h2>🌍 World Management Flow</h2>
        <div class="description">Complete server-side flow from startup through world creation, client handling, command processing, and all world management operations.</div>
        <div class="export-buttons">
            <a href="world-management-flow.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="world-management-flow">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-world-management-flow">
flowchart TD
    Start([Server Startup]) --> LoadConfig[Load World Configuration]
    LoadConfig --> ValidateConfig{Config Valid?}
    ValidateConfig -->|No| ConfigError[Configuration Error - Exit]
    ValidateConfig -->|Yes| CreateWorld[Create World Instance]
    
    CreateWorld --> SetDimensions[Set World Dimensions]
    SetDimensions --> LoadObstacles[Load Obstacle Definitions]
    LoadObstacles --> ValidateObstacles{Obstacles Valid?}
    ValidateObstacles -->|Overlap| ObstacleError[Obstacle Overlap Error]
    ValidateObstacles -->|Valid| PlaceObstacles[Place Obstacles in World]
    
    PlaceObstacles --> StartServer[Start TCP Server]
    StartServer --> ListenLoop{Listen for Connections}
    
    ListenLoop --> ClientConnect[Client Connection]
    ClientConnect --> CreateHandler[Create Client Handler Thread]
    CreateHandler --> MessageLoop{Message Processing Loop}
    
    MessageLoop --> ReceiveMsg[Receive Client Message]
    ReceiveMsg --> ParseJSON{Valid JSON?}
    ParseJSON -->|No| JSONError[JSON Parse Error]
    ParseJSON -->|Yes| RouteCommand[Route Command]
    
    RouteCommand --> CommandType{Command Type?}
    
    CommandType -->|launch| LaunchRobot[Launch Robot Process]
    CommandType -->|move| MoveRobot[Movement Process]
    CommandType -->|fire| CombatProcess[Combat Process]
    CommandType -->|look| VisionProcess[Vision Process]
    CommandType -->|repair| RepairProcess[Repair Process]
    CommandType -->|reload| ReloadProcess[Reload Process]
    CommandType -->|state| StateQuery[State Query Process]
    CommandType -->|quit| ClientDisconnect[Disconnect Client]
    
    LaunchRobot --> ValidateName{Name Available?}
    ValidateName -->|Taken| NameError[Name Taken Error]
    ValidateName -->|Available| FindSpawn[Find Spawn Position]
    FindSpawn --> SpawnFound{Valid Spawn?}
    SpawnFound -->|No| SpawnError[No Spawn Available]
    SpawnFound -->|Yes| CreateRobotInstance[Create Robot Instance]
    CreateRobotInstance --> AddToWorld[Add Robot to World]
    AddToWorld --> LaunchSuccess[Launch Success Response]
    
    VisionProcess --> GetRobotPos[Get Robot Position]
    GetRobotPos --> CalcVisibility[Calculate Visibility Range]
    CalcVisibility --> ScanDirections[Scan All Directions]
    ScanDirections --> CheckLineOfSight[Check Line of Sight]
    CheckLineOfSight --> BuildVisionResult[Build Vision Result]
    BuildVisionResult --> VisionResponse[Return Vision Response]
    
    RepairProcess --> StartRepair[Start Repair Timer]
    StartRepair --> RepairWait[Wait for Repair Duration]
    RepairWait --> RestoreShield[Restore Shield to Max]
    RestoreShield --> RepairComplete[Repair Complete Response]
    
    ReloadProcess --> StartReload[Start Reload Timer]
    StartReload --> ReloadWait[Wait for Reload Duration]
    ReloadWait --> RestoreAmmo[Restore Ammo to Max]
    RestoreAmmo --> ReloadComplete[Reload Complete Response]
    
    StateQuery --> GetRobotState[Get Current Robot State]
    GetRobotState --> BuildStateResponse[Build State Response]
    BuildStateResponse --> StateResponse[Return State Response]
    
    ClientDisconnect --> RemoveRobots[Remove Client's Robots]
    RemoveRobots --> CloseConnection[Close Connection]
    CloseConnection --> ListenLoop
    
    %% All responses flow back to message loop
    LaunchSuccess --> MessageLoop
    VisionResponse --> MessageLoop
    RepairComplete --> MessageLoop
    ReloadComplete --> MessageLoop
    StateResponse --> MessageLoop
    JSONError --> MessageLoop
    NameError --> MessageLoop
    SpawnError --> MessageLoop
    
    %% Error handling
    ConfigError --> Exit([Server Exit])
    ObstacleError --> Exit

            </div>
        </div>
    </div>

    <!-- 🔄 SYSTEM INTERACTIONS -->
    <div class="category-header" id="interactions">
        <h2>🔄 System Interactions</h2>
        <p>Sequence diagrams showing component interactions</p>
    </div>

    <div class="diagram-container" id="robot-launch-sequence">
        <h2>🚀 Robot Launch Sequence</h2>
        <div class="description">Sequence diagram showing the interaction between User, Client, Server, World, RobotManager, and Robot during the robot launch process.</div>
        <div class="export-buttons">
            <a href="robot-launch-sequence.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="robot-launch-sequence">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-robot-launch-sequence">
sequenceDiagram
    participant User
    participant Client
    participant Server
    participant World
    participant RobotManager
    participant Robot

    User->>Client: Start client with server IP:port
    Client->>Server: TCP Connection Request
    Server-->>Client: Connection Established
    
    User->>Client: launch Sniper "RobotName"
    Client->>Client: Validate command format
    Client->>Server: {"command": "launch", "make": "Sniper", "name": "RobotName"}
    
    Server->>Server: Parse JSON message
    Server->>RobotManager: checkNameAvailable("RobotName")
    RobotManager-->>Server: true/false
    
    alt Name Available
        Server->>World: findSpawnPosition()
        World-->>Server: Position(x, y)
        
        alt Spawn Position Found
            Server->>Robot: new Robot("RobotName", "Sniper", position)
            Robot-->>Server: Robot instance created
            Server->>RobotManager: addRobot(robot)
            RobotManager->>World: placeRobot(robot, position)
            World-->>RobotManager: Robot placed
            RobotManager-->>Server: Robot added successfully
            
            Server-->>Client: {"result": "OK", "data": {"position": [x,y], "state": "NORMAL"}}
            Client-->>User: "Robot launched successfully at [x,y]"
            
        else No Spawn Position
            Server-->>Client: {"result": "ERROR", "message": "No spawn position available"}
            Client-->>User: "Error: World is full"
        end
        
    else Name Taken
        Server-->>Client: {"result": "ERROR", "message": "Name already taken"}
        Client-->>User: "Error: Robot name already exists"
    end

            </div>
        </div>
    </div>

    <div class="diagram-container" id="combat-sequence">
        <h2>💥 Combat Sequence</h2>
        <div class="description">Detailed sequence diagram showing the interaction between two players during combat, including firing, hit detection, shield damage, and death scenarios.</div>
        <div class="export-buttons">
            <a href="combat-sequence.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="combat-sequence">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-combat-sequence">
sequenceDiagram
    participant User1 as User (Attacker)
    participant Client1 as Client (Attacker)
    participant Server
    participant World
    participant Robot1 as Robot (Attacker)
    participant Robot2 as Robot (Target)
    participant Client2 as Client (Target)
    participant User2 as User (Target)

    User1->>Client1: fire
    Client1->>Server: {"command": "fire"}
    
    Server->>Robot1: canFire()
    Robot1-->>Server: true (has ammo, not reloading)
    
    Server->>Robot1: getPosition()
    Robot1-->>Server: Position(x1, y1)
    Server->>Robot1: getDirection()
    Robot1-->>Server: NORTH
    Server->>Robot1: getWeaponRange()
    Robot1-->>Server: 5 steps
    
    Server->>World: traceShot(position, direction, range)
    World->>World: Calculate shot path
    World->>World: Check for obstacles in path
    World->>World: Find first robot in path
    World-->>Server: Robot2 at Position(x2, y2)
    
    Server->>Robot1: reduceAmmo()
    Robot1-->>Server: Ammo reduced
    
    Server->>Robot2: takeDamage()
    Robot2->>Robot2: Check shield strength
    
    alt Shield Absorbs Hit
        Robot2-->>Server: Shield damaged, robot alive
        Server-->>Client1: {"result": "OK", "message": "Hit target, shield absorbed damage"}
        Client1-->>User1: "Hit! Target's shield damaged"
        
        Note over Robot2: Shield strength reduced
        
    else Shield Destroyed - Robot Dies
        Robot2-->>Server: Robot destroyed
        Server->>World: removeRobot(Robot2)
        World-->>Server: Robot removed
        
        Server-->>Client1: {"result": "OK", "message": "Target destroyed!"}
        Client1-->>User1: "KILL! Target destroyed!"
        
        Server-->>Client2: {"result": "DEAD", "message": "Your robot was destroyed"}
        Client2-->>User2: "Your robot was killed by enemy fire!"
        
    else No Shield - Direct Kill
        Robot2-->>Server: Robot destroyed (no shield)
        Server->>World: removeRobot(Robot2)
        World-->>Server: Robot removed
        
        Server-->>Client1: {"result": "OK", "message": "Target destroyed!"}
        Client1-->>User1: "KILL! Target destroyed!"
        
        Server-->>Client2: {"result": "DEAD", "message": "Your robot was destroyed"}
        Client2-->>User2: "Your robot was killed by enemy fire!"
    end

            </div>
        </div>
    </div>

    <div class="diagram-container" id="client-server-interaction">
        <h2>🔄 Client-Server Interaction</h2>
        <div class="description">General sequence diagram showing the standard pattern of interaction between client and server components for all types of commands.</div>
        <div class="export-buttons">
            <a href="client-server-interaction.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="client-server-interaction">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-client-server-interaction">
sequenceDiagram
    participant User
    participant CLI
    participant Client
    participant NetworkLayer
    participant Server
    participant CommandProcessor
    participant WorldEngine
    participant World

    User->>CLI: Enter command
    CLI->>CLI: Parse user input
    CLI->>Client: processCommand(command)
    
    Client->>Client: Validate command
    Client->>NetworkLayer: sendMessage(jsonCommand)
    NetworkLayer->>Server: TCP message
    
    Server->>CommandProcessor: parseMessage(json)
    CommandProcessor->>CommandProcessor: Validate JSON
    CommandProcessor->>CommandProcessor: Route command
    
    alt Robot Command
        CommandProcessor->>WorldEngine: executeRobotCommand(command)
        WorldEngine->>World: performAction(robot, action)
        World-->>WorldEngine: ActionResult
        WorldEngine-->>CommandProcessor: CommandResult
        
    else World Command  
        CommandProcessor->>WorldEngine: executeWorldCommand(command)
        WorldEngine->>World: getWorldState()
        World-->>WorldEngine: WorldState
        WorldEngine-->>CommandProcessor: WorldData
    end
    
    CommandProcessor->>Server: formatResponse(result)
    Server->>NetworkLayer: TCP response
    NetworkLayer-->>Client: JSON response
    
    Client->>Client: Process response
    Client->>CLI: updateDisplay(response)
    CLI-->>User: Show result
    
    Note over User, World: This pattern repeats for all commands:<br/>look, move, fire, repair, reload, state, etc.

            </div>
        </div>
    </div>

    <!-- 📊 STATE MANAGEMENT -->
    <div class="category-header" id="state-management">
        <h2>📊 State Management</h2>
        <p>State diagrams showing system and component states</p>
    </div>

    <div class="diagram-container" id="robot-state-diagram">
        <h2>🤖 Robot State Diagram</h2>
        <div class="description">State diagram showing all possible robot states including Normal, Moving, Repairing, Reloading, and Dead, with transitions between states.</div>
        <div class="export-buttons">
            <a href="robot-state-diagram.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="robot-state-diagram">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-robot-state-diagram">
stateDiagram-v2
    [*] --> NotLaunched : Robot Created
    
    NotLaunched --> Normal : Launch Command
    
    Normal --> Moving : Move Command
    Moving --> Normal : Movement Complete
    Moving --> Dead : Fall into Pit
    
    Normal --> Repairing : Repair Command
    Repairing --> Normal : Repair Complete
    Repairing --> Dead : Take Fatal Damage
    
    Normal --> Reloading : Reload Command
    Reloading --> Normal : Reload Complete
    Reloading --> Dead : Take Fatal Damage
    
    Normal --> Dead : Take Fatal Damage
    Normal --> Dead : Shield Destroyed
    
    Moving --> Dead : Take Fatal Damage
    
    Dead --> [*] : Remove from World
    
    state Normal {
        [*] --> Ready
        Ready --> Firing : Fire Command
        Firing --> Ready : Shot Complete
        Ready --> Looking : Look Command
        Looking --> Ready : Look Complete
        Ready --> TurningLeft : Turn Left
        Ready --> TurningRight : Turn Right
        TurningLeft --> Ready : Turn Complete
        TurningRight --> Ready : Turn Complete
    }
    
    state Moving {
        [*] --> ValidatingPath
        ValidatingPath --> MovingForward : Path Clear
        ValidatingPath --> MovingBackward : Path Clear
        ValidatingPath --> MovementError : Path Blocked
        MovingForward --> [*] : Destination Reached
        MovingBackward --> [*] : Destination Reached
        MovementError --> [*] : Error Reported
    }
    
    state Repairing {
        [*] --> RepairInProgress
        RepairInProgress --> [*] : Timer Complete
        note right of RepairInProgress : Cannot move or fire\nwhile repairing
    }
    
    state Reloading {
        [*] --> ReloadInProgress
        ReloadInProgress --> [*] : Timer Complete
        note right of ReloadInProgress : Cannot move or fire\nwhile reloading
    }

            </div>
        </div>
    </div>

    <div class="diagram-container" id="world-state-diagram">
        <h2>🗺️ World State Diagram</h2>
        <div class="description">State diagram showing world states from initialization through active gameplay, including error handling and shutdown procedures.</div>
        <div class="export-buttons">
            <a href="world-state-diagram.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="world-state-diagram">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-world-state-diagram">
stateDiagram-v2
    [*] --> Initializing : Server Start
    
    Initializing --> LoadingConfig : Load Configuration
    LoadingConfig --> ConfigError : Invalid Config
    LoadingConfig --> CreatingWorld : Valid Config
    
    ConfigError --> [*] : Server Exit
    
    CreatingWorld --> PlacingObstacles : World Created
    PlacingObstacles --> ObstacleError : Obstacle Overlap
    PlacingObstacles --> WaitingForClients : Obstacles Placed
    
    ObstacleError --> [*] : Server Exit
    
    WaitingForClients --> Active : First Client Connected
    
    Active --> Processing : Command Received
    Processing --> Active : Command Processed
    
    Active --> Shutdown : Quit Command
    Active --> WaitingForClients : All Clients Disconnected
    
    Shutdown --> [*] : Server Exit
    
    state Active {
        [*] --> Idle
        
        Idle --> RobotLaunching : Launch Command
        RobotLaunching --> Idle : Robot Launched
        RobotLaunching --> Idle : Launch Failed
        
        Idle --> RobotMoving : Move Command
        RobotMoving --> Idle : Movement Complete
        RobotMoving --> RobotDestroyed : Robot Falls in Pit
        
        Idle --> Combat : Fire Command
        Combat --> Idle : Shot Processed
        Combat --> RobotDestroyed : Robot Killed
        
        Idle --> RobotRepairing : Repair Command
        RobotRepairing --> Idle : Repair Complete
        
        Idle --> RobotReloading : Reload Command
        RobotReloading --> Idle : Reload Complete
        
        Idle --> QueryProcessing : Look/State Command
        QueryProcessing --> Idle : Query Response Sent
        
        RobotDestroyed --> Idle : Robot Removed
    }
    
    state Processing {
        [*] --> MessageParsing
        MessageParsing --> InvalidMessage : JSON Error
        MessageParsing --> CommandRouting : Valid JSON
        
        InvalidMessage --> [*] : Error Response
        
        CommandRouting --> RobotCommand : Robot Action
        CommandRouting --> WorldCommand : World Query
        CommandRouting --> UnknownCommand : Invalid Command
        
        RobotCommand --> [*] : Action Executed
        WorldCommand --> [*] : Query Processed
        UnknownCommand --> [*] : Error Response
    }

            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#007acc',
                primaryTextColor: '#333',
                primaryBorderColor: '#007acc',
                lineColor: '#666'
            }
        });

        // Zoom functionality for diagrams
        let zoomLevels = new Map(); // Store zoom level for each diagram
        let isDragging = false;
        let dragStart = { x: 0, y: 0 };
        let scrollStart = { x: 0, y: 0 };

        function initializeZoom() {
            const diagramWrappers = document.querySelectorAll('.diagram-wrapper');

            diagramWrappers.forEach(wrapper => {
                const diagramId = wrapper.dataset.diagramId;
                zoomLevels.set(diagramId, 1.0);

                // Zoom controls
                const zoomInBtn = wrapper.querySelector('.zoom-in');
                const zoomOutBtn = wrapper.querySelector('.zoom-out');
                const resetBtn = wrapper.querySelector('.reset-btn');
                const fullscreenBtn = wrapper.querySelector('.fullscreen-btn');

                zoomInBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    zoomDiagram(diagramId, 0.2);
                });

                zoomOutBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    zoomDiagram(diagramId, -0.2);
                });

                resetBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    resetZoom(diagramId);
                });

                fullscreenBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    openFullscreen(diagramId);
                });

                // Mouse wheel zoom
                wrapper.addEventListener('wheel', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        const delta = e.deltaY > 0 ? -0.1 : 0.1;
                        zoomDiagram(diagramId, delta);
                    }
                });

                // Pan functionality
                wrapper.addEventListener('mousedown', (e) => {
                    if (zoomLevels.get(diagramId) > 1) {
                        isDragging = true;
                        dragStart.x = e.clientX;
                        dragStart.y = e.clientY;
                        scrollStart.x = wrapper.scrollLeft;
                        scrollStart.y = wrapper.scrollTop;
                        wrapper.style.cursor = 'grabbing';
                    }
                });

                wrapper.addEventListener('mousemove', (e) => {
                    if (isDragging) {
                        e.preventDefault();
                        const deltaX = e.clientX - dragStart.x;
                        const deltaY = e.clientY - dragStart.y;
                        wrapper.scrollLeft = scrollStart.x - deltaX;
                        wrapper.scrollTop = scrollStart.y - deltaY;
                    }
                });

                wrapper.addEventListener('mouseup', () => {
                    isDragging = false;
                    wrapper.style.cursor = zoomLevels.get(diagramId) > 1 ? 'grab' : 'default';
                });

                wrapper.addEventListener('mouseleave', () => {
                    isDragging = false;
                    wrapper.style.cursor = zoomLevels.get(diagramId) > 1 ? 'grab' : 'default';
                });
            });
        }

        function zoomDiagram(diagramId, delta) {
            const currentZoom = zoomLevels.get(diagramId);
            const newZoom = Math.max(0.5, Math.min(3.0, currentZoom + delta));
            zoomLevels.set(diagramId, newZoom);

            const wrapper = document.querySelector(`[data-diagram-id="${diagramId}"]`);
            const mermaidElement = wrapper.querySelector('.mermaid');
            const indicator = wrapper.querySelector('.zoom-indicator');

            mermaidElement.style.transform = `scale(${newZoom})`;
            indicator.textContent = `${Math.round(newZoom * 100)}%`;

            // Add/remove zoomed class for styling
            if (newZoom > 1) {
                wrapper.classList.add('zoomed');
                wrapper.style.cursor = 'grab';
            } else {
                wrapper.classList.remove('zoomed');
                wrapper.style.cursor = 'default';
            }
        }

        function resetZoom(diagramId) {
            zoomLevels.set(diagramId, 1.0);
            const wrapper = document.querySelector(`[data-diagram-id="${diagramId}"]`);
            const mermaidElement = wrapper.querySelector('.mermaid');
            const indicator = wrapper.querySelector('.zoom-indicator');

            mermaidElement.style.transform = 'scale(1)';
            indicator.textContent = '100%';
            wrapper.classList.remove('zoomed');
            wrapper.style.cursor = 'default';
            wrapper.scrollLeft = 0;
            wrapper.scrollTop = 0;
        }

        function openFullscreen(diagramId) {
            const originalWrapper = document.querySelector(`[data-diagram-id="${diagramId}"]`);
            const originalMermaid = originalWrapper.querySelector('.mermaid');
            const modal = document.getElementById('fullscreen-modal');
            const fullscreenDiagram = document.getElementById('fullscreen-diagram');

            // Clone the diagram for fullscreen
            const clonedMermaid = originalMermaid.cloneNode(true);
            clonedMermaid.style.transform = 'scale(1)';
            clonedMermaid.style.maxWidth = 'none';
            clonedMermaid.style.maxHeight = 'none';

            fullscreenDiagram.innerHTML = '';
            fullscreenDiagram.appendChild(clonedMermaid);

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeFullscreen() {
            const modal = document.getElementById('fullscreen-modal');
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Close fullscreen on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeFullscreen();
            }
        });

        // Close fullscreen on background click
        document.getElementById('fullscreen-modal').addEventListener('click', (e) => {
            if (e.target.id === 'fullscreen-modal') {
                closeFullscreen();
            }
        });

        // Enhanced navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize zoom functionality
            initializeZoom();
            // Get all navigation links
            const navLinks = document.querySelectorAll('.nav-link, .toc a');
            const sections = document.querySelectorAll('.category-header, .diagram-container');

            // Add click handlers for smooth scrolling with proper offset
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // Calculate offset for sticky navigation
                        const offset = 120;
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - offset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });

                        // Update active states
                        updateActiveNavigation(targetId);
                    }
                });
            });

            // Function to update active navigation states
            function updateActiveNavigation(activeId) {
                // Remove all active classes
                navLinks.forEach(link => {
                    link.classList.remove('active');
                });

                // Add active class to current links
                navLinks.forEach(link => {
                    if (link.getAttribute('href') === '#' + activeId) {
                        link.classList.add('active');
                    }
                });
            }

            // Intersection Observer for automatic highlighting during scroll
            const observerOptions = {
                root: null,
                rootMargin: '-120px 0px -50% 0px',
                threshold: 0
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateActiveNavigation(entry.target.id);
                    }
                });
            }, observerOptions);

            // Observe all sections
            sections.forEach(section => {
                if (section.id) {
                    observer.observe(section);
                }
            });
        });

        // Make closeFullscreen globally accessible for the close button
        window.closeFullscreen = closeFullscreen;

        // Add keyboard shortcuts info
        console.log('🔍 Zoom Controls:');
        console.log('• Click +/- buttons to zoom in/out');
        console.log('• Ctrl/Cmd + Mouse Wheel to zoom');
        console.log('• Click house icon to reset zoom');
        console.log('• Click fullscreen icon for fullscreen view');
        console.log('• Drag to pan when zoomed in');
        console.log('• Press Escape to exit fullscreen');
    </script>
</body>
</html>