flowchart TD
    Start([Movement Command Received]) --> Parse[Parse Command<br/>forward/back/turn]
    Parse --> ValidateCmd{Valid Command?}
    ValidateCmd -->|No| ErrorResp[Return Error Response]
    ValidateCmd -->|Yes| GetRobot[Get Robot from World]
    
    GetRobot --> RobotExists{Robot Exists?}
    RobotExists -->|No| NotFound[Robot Not Found Error]
    RobotExists -->|Yes| CheckState{Robot Alive?}
    
    CheckState -->|Dead| DeadError[Robot is Dead Error]
    CheckState -->|Repairing| BusyError[Robot is Repairing Error]
    CheckState -->|Reloading| BusyError
    CheckState -->|Ready| ProcessMove[Process Movement]
    
    ProcessMove --> MoveType{Movement Type?}
    
    MoveType -->|Turn| UpdateDirection[Update Robot Direction]
    UpdateDirection --> Success[Return Success Response]
    
    MoveType -->|Forward/Back| CalcNewPos[Calculate New Position]
    CalcNewPos --> ValidateSteps{Valid Step Count?}
    ValidateSteps -->|No| InvalidSteps[Invalid Steps Error]
    ValidateSteps -->|Yes| CheckPath[Check Movement Path]
    
    CheckPath --> PathLoop{For Each Step}
    PathLoop --> CheckBounds{Within World Bounds?}
    CheckBounds -->|No| EdgeError[Hit World Edge Error]
    CheckBounds -->|Yes| CheckObstacle{Obstacle in Path?}
    
    CheckObstacle -->|Mountain/Lake| BlockedError[Path Blocked Error]
    CheckObstacle -->|Bottomless Pit| FallIntoPit[Robot Falls & Dies]
    CheckObstacle -->|None| CheckRobot{Other Robot Here?}
    
    CheckRobot -->|Yes| CollisionError[Robot Collision Error]
    CheckRobot -->|No| NextStep{More Steps?}
    
    NextStep -->|Yes| PathLoop
    NextStep -->|No| UpdatePosition[Update Robot Position]
    UpdatePosition --> Success
    
    FallIntoPit --> RemoveRobot[Remove Robot from World]
    RemoveRobot --> DeathResp[Return Death Response]
    
    ErrorResp --> End([End])
    NotFound --> End
    DeadError --> End
    BusyError --> End
    InvalidSteps --> End
    EdgeError --> End
    BlockedError --> End
    CollisionError --> End
    Success --> End
    DeathResp --> End
