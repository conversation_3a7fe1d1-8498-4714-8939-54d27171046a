graph TB
    subgraph "Robot World Demo - 20x20 Grid"
        subgraph "Legend"
            L1[🤖 Robot - Player controlled units]
            L2[🏔️ Mountain - Blocks movement & vision]
            L3[🌊 Lake - Blocks movement only]
            L4[🕳️ Pit - Destroys robots]
            L5[⚡ Combat - Weapon fire & hits]
            L6[👁️ Vision - Line of sight range]
        end
        
        subgraph "Sample World Layout"
            subgraph "North Section (0-5)"
                N1["(0,0) 🏔️ (1,0) 🏔️ (2,0) . (3,0) . (4,0) ."]
                N2["(0,1) 🏔️ (1,1) 🏔️ (2,1) . (3,1) . (4,1) ."]
                N3["(0,2) . (1,2) . (2,2) . (3,2) 🤖 (4,2) ."]
                N4["(0,3) . (1,3) . (2,3) . (3,3) ↑ (4,3) ."]
                N5["(0,4) . (1,4) . (2,4) . (3,4) . (4,4) ."]
            end
            
            subgraph "Central Section (5-10)"
                C1["(0,5) . (1,5) . (2,5) 🌊 (3,5) 🌊 (4,5) ."]
                C2["(0,6) . (1,6) . (2,6) 🌊 (3,6) 🌊 (4,6) ."]
                C3["(0,7) . (1,7) . (2,7) . (3,7) . (4,7) ."]
                C4["(0,8) . (1,8) 🕳️ (2,8) . (3,8) . (4,8) 🤖"]
                C5["(0,9) . (1,9) . (2,9) . (3,9) . (4,9) ←"]
            end
            
            subgraph "South Section (10-15)"
                S1["(0,10) . (1,10) . (2,10) . (3,10) 🏔️ (4,10) 🏔️"]
                S2["(0,11) . (1,11) . (2,11) . (3,11) 🏔️ (4,11) 🏔️"]
                S3["(0,12) 🤖 (1,12) . (2,12) . (3,12) . (4,12) ."]
                S4["(0,13) → (1,13) . (2,13) . (3,13) . (4,13) ."]
                S5["(0,14) . (1,14) . (2,14) . (3,14) . (4,14) ."]
            end
        end
        
        subgraph "Game Mechanics Demo"
            subgraph "Combat Example"
                CE1[Robot A at (3,2) facing NORTH]
                CE2[Robot B at (4,8) facing WEST]
                CE3[Robot C at (0,12) facing EAST]
                CE4["🎯 Robot A can see Robot B (no obstacles)"]
                CE5["❌ Robot A cannot see Robot C (mountain blocks)"]
                CE6["⚔️ Robot B fires at Robot A - 25 unit range"]
            end
            
            subgraph "Movement Example"
                ME1["🚶 Robot C moves EAST from (0,12)"]
                ME2["✅ (1,12) - Valid move"]
                ME3["✅ (2,12) - Valid move"]
                ME4["❌ (1,8) - Blocked by pit"]
                ME5["❌ (3,10) - Blocked by mountain"]
            end
            
            subgraph "Vision Example"
                VE1["👁️ Robot A looks around from (3,2)"]
                VE2["NORTH: Clear for 50 units"]
                VE3["EAST: Robot B visible at 25 units"]
                VE4["SOUTH: Lake visible at 15 units"]
                VE5["WEST: Clear for 15 units"]
            end
        end
        
        subgraph "Robot Status Examples"
            subgraph "Sniper Robot"
                SR1["🎯 Name: 'SharpShooter'"]
                SR2["📍 Position: (3,2)"]
                SR3["🧭 Direction: NORTH"]
                SR4["🛡️ Shield: 2/2"]
                SR5["🔫 Ammo: 3/3"]
                SR6["📏 Range: 100 units"]
                SR7["⚡ Status: NORMAL"]
            end
            
            subgraph "Tank Robot"
                TR1["🛡️ Name: 'IronFist'"]
                TR2["📍 Position: (4,8)"]
                TR3["🧭 Direction: WEST"]
                TR4["🛡️ Shield: 5/5"]
                TR5["🔫 Ammo: 8/8"]
                TR6["📏 Range: 30 units"]
                TR7["⚡ Status: NORMAL"]
            end
        end
        
        subgraph "Command Examples"
            subgraph "Basic Commands"
                BC1["launch sniper MyBot"]
                BC2["move 5"]
                BC3["turn left"]
                BC4["look"]
                BC5["fire"]
                BC6["repair"]
                BC7["reload"]
                BC8["state"]
            end
            
            subgraph "World Commands"
                WC1["robots - List all robots"]
                WC2["dump - Show world state"]
                WC3["quit - Disconnect"]
            end
        end
    end
    
    %% Relationships showing game flow
    CE1 --> CE4
    CE2 --> CE6
    ME1 --> ME2
    ME2 --> ME3
    VE1 --> VE2
    VE1 --> VE3
    VE1 --> VE4
    VE1 --> VE5
    
    %% Robot status connections
    SR1 --> SR2
    SR2 --> SR3
    TR1 --> TR2
    TR2 --> TR3
    
    %% Command flow
    BC1 --> BC2
    BC2 --> BC3
    BC3 --> BC4
    BC4 --> BC5
    
    %% Legend connections
    L1 -.-> SR1
    L2 -.-> N1
    L3 -.-> C1
    L4 -.-> C4
    L5 -.-> CE6
    L6 -.-> VE1
