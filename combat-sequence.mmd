sequenceDiagram
    participant User<PERSON> as User (Attacker)
    participant C<PERSON><PERSON> as Client (Attacker)
    participant Server
    participant World
    participant Robot1 as Robot (Attacker)
    participant Robot2 as Robot (Target)
    participant Client2 as Client (Target)
    participant User2 as User (Target)

    User1->>Client1: fire
    Client1->>Server: {"command": "fire"}
    
    Server->>Robot1: canFire()
    Robot1-->>Server: true (has ammo, not reloading)
    
    Server->>Robot1: getPosition()
    Robot1-->>Server: Position(x1, y1)
    Server->>Robot1: getDirection()
    Robot1-->>Server: NORTH
    Server->>Robot1: getWeaponRange()
    Robot1-->>Server: 5 steps
    
    Server->>World: traceShot(position, direction, range)
    World->>World: Calculate shot path
    World->>World: Check for obstacles in path
    World->>World: Find first robot in path
    World-->>Server: Robot2 at Position(x2, y2)
    
    Server->>Robot1: reduceAmmo()
    Robot1-->>Server: Ammo reduced
    
    Server->>Robot2: takeDamage()
    Robot2->>Robot2: Check shield strength
    
    alt Shield Absorbs Hit
        Robot2-->>Server: Shield damaged, robot alive
        Server-->>Client1: {"result": "OK", "message": "Hit target, shield absorbed damage"}
        Client1-->>User1: "Hit! Target's shield damaged"
        
        Note over Robot2: Shield strength reduced
        
    else Shield Destroyed - Robot Dies
        Robot2-->>Server: Robot destroyed
        Server->>World: removeRobot(Robot2)
        World-->>Server: Robot removed
        
        Server-->>Client1: {"result": "OK", "message": "Target destroyed!"}
        Client1-->>User1: "KILL! Target destroyed!"
        
        Server-->>Client2: {"result": "DEAD", "message": "Your robot was destroyed"}
        Client2-->>User2: "Your robot was killed by enemy fire!"
        
    else No Shield - Direct Kill
        Robot2-->>Server: Robot destroyed (no shield)
        Server->>World: removeRobot(Robot2)
        World-->>Server: Robot removed
        
        Server-->>Client1: {"result": "OK", "message": "Target destroyed!"}
        Client1-->>User1: "KILL! Target destroyed!"
        
        Server-->>Client2: {"result": "DEAD", "message": "Your robot was destroyed"}
        Client2-->>User2: "Your robot was killed by enemy fire!"
    end
