#!/usr/bin/env python3
"""
Script to generate a complete HTML file with all Robot World diagrams
"""

import os

def read_mermaid_file(filename):
    """Read a mermaid file and return its content"""
    try:
        with open(filename, 'r') as f:
            return f.read()
    except FileNotFoundError:
        return f"<!-- {filename} not found -->"

def generate_html():
    """Generate the complete HTML file with all diagrams"""
    
    # Read all mermaid files
    diagrams = {
        'class-diagram': {
            'title': 'Class Diagram',
            'description': 'Shows the most important classes and their relationships in the Robot World system. This diagram defines the core object-oriented structure including World, Robot, Obstacles, Shield, Weapon, and their interactions.',
            'category': 'architecture',
            'icon': '🏗️'
        },
        'domain-diagram': {
            'title': 'Domain Diagram', 
            'description': 'Characterizes the domain classes and how they work together, showing business concepts and their interactions across different domains like Game World, Robot, Combat, Obstacles, Configuration, and Communication.',
            'category': 'architecture',
            'icon': '🌐'
        },
        'client-component-diagram': {
            'title': 'Client Component Diagram',
            'description': 'Shows the architecture and components of the robot client application, including User Interface, Application, Domain, Communication, and Configuration layers.',
            'category': 'architecture', 
            'icon': '💻'
        },
        'server-component-diagram': {
            'title': 'Server Component Diagram',
            'description': 'Shows the server architecture for the portion that reads and executes commands, including Network, Command Processing, Game Engine, Domain, Configuration, and Persistence layers.',
            'category': 'architecture',
            'icon': '🖥️'
        },
        'user-journey-storyboard': {
            'title': 'User Journey Storyboard',
            'description': 'Visual storyboard showing the complete user experience from setup and launch through exploration, combat preparation, combat, and victory/defeat scenarios.',
            'category': 'user-experience',
            'icon': '📖'
        },
        'user-story-flow': {
            'title': 'User Story Flow',
            'description': 'Detailed flowchart showing the complete user interaction flow from starting the client through all possible game scenarios and decision points.',
            'category': 'user-experience',
            'icon': '🎯'
        },
        'robot-movement-flow': {
            'title': 'Robot Movement Flow',
            'description': 'System flow diagram showing how robot movement commands are processed, including validation, path checking, obstacle detection, and collision handling.',
            'category': 'system-flows',
            'icon': '🚶'
        },
        'combat-resolution-flow': {
            'title': 'Combat Resolution Flow',
            'description': 'Detailed flow showing how combat is resolved, including ammunition checks, shot tracing, hit detection, shield damage, and robot destruction.',
            'category': 'system-flows',
            'icon': '⚔️'
        },
        'world-management-flow': {
            'title': 'World Management Flow',
            'description': 'Complete server-side flow from startup through world creation, client handling, command processing, and all world management operations.',
            'category': 'system-flows',
            'icon': '🌍'
        },
        'robot-launch-sequence': {
            'title': 'Robot Launch Sequence',
            'description': 'Sequence diagram showing the interaction between User, Client, Server, World, RobotManager, and Robot during the robot launch process.',
            'category': 'interactions',
            'icon': '🚀'
        },
        'combat-sequence': {
            'title': 'Combat Sequence',
            'description': 'Detailed sequence diagram showing the interaction between two players during combat, including firing, hit detection, shield damage, and death scenarios.',
            'category': 'interactions',
            'icon': '💥'
        },
        'client-server-interaction': {
            'title': 'Client-Server Interaction',
            'description': 'General sequence diagram showing the standard pattern of interaction between client and server components for all types of commands.',
            'category': 'interactions',
            'icon': '🔄'
        },
        'robot-state-diagram': {
            'title': 'Robot State Diagram',
            'description': 'State diagram showing all possible robot states including Normal, Moving, Repairing, Reloading, and Dead, with transitions between states.',
            'category': 'state-management',
            'icon': '🤖'
        },
        'world-state-diagram': {
            'title': 'World State Diagram',
            'description': 'State diagram showing world states from initialization through active gameplay, including error handling and shutdown procedures.',
            'category': 'state-management',
            'icon': '🗺️'
        }
    }
    
    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robot World - Complete Design Documentation</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            line-height: 1.6;
        }
        .diagram-container {
            background: white;
            margin: 30px 0;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #007acc;
        }
        .nav-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            position: sticky;
            top: 20px;
            z-index: 100;
        }
        .nav-links {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }
        .nav-link {
            background: #007acc;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.3s;
        }
        .nav-link:hover {
            background: #005a9e;
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #007acc;
            border-bottom: 2px solid #ddd;
            padding-bottom: 8px;
            margin-top: 0;
            font-size: 1.8em;
        }
        .description {
            color: #666;
            font-style: italic;
            margin-bottom: 20px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid #007acc;
        }
        .mermaid {
            text-align: center;
            background: #fafbfc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .export-buttons {
            text-align: center;
            margin: 15px 0;
        }
        .export-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 8px;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background 0.3s;
        }
        .export-btn:hover {
            background: #218838;
        }
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 40px 0 20px 0;
            text-align: center;
        }
        .category-header h2 {
            margin: 0;
            border: none;
            color: white;
        }
        .toc {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin-top: 0;
            color: #495057;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #007acc;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            display: block;
            transition: background 0.3s;
        }
        .toc a:hover {
            background: #007acc;
            color: white;
        }

        /* Smooth scrolling for all anchor links */
        html {
            scroll-behavior: smooth;
        }

        /* Offset for sticky navigation */
        .diagram-container, .category-header {
            scroll-margin-top: 120px;
        }

        /* Highlight active navigation links */
        .nav-link.active {
            background: #005a9e;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toc a.active {
            background: #007acc;
            color: white;
        }

        /* Zoom functionality styles */
        .diagram-wrapper {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            background: #fafbfc;
            cursor: grab;
        }

        .diagram-wrapper:active {
            cursor: grabbing;
        }

        .diagram-wrapper.zoomed {
            overflow: auto;
            max-height: 600px;
        }

        .mermaid {
            transition: transform 0.3s ease;
            transform-origin: center center;
        }

        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .diagram-wrapper:hover .zoom-controls {
            opacity: 1;
        }

        .zoom-btn {
            background: rgba(0, 122, 204, 0.9);
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .zoom-btn:hover {
            background: rgba(0, 90, 158, 0.9);
            transform: scale(1.1);
        }

        .zoom-btn:active {
            transform: scale(0.95);
        }

        .fullscreen-btn {
            background: rgba(40, 167, 69, 0.9);
        }

        .fullscreen-btn:hover {
            background: rgba(33, 136, 56, 0.9);
        }

        .reset-btn {
            background: rgba(108, 117, 125, 0.9);
        }

        .reset-btn:hover {
            background: rgba(90, 98, 104, 0.9);
        }

        /* Fullscreen modal styles */
        .fullscreen-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            overflow: auto;
        }

        .fullscreen-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fullscreen-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 95%;
            max-height: 95%;
            overflow: auto;
            position: relative;
        }

        .fullscreen-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #dc3545;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            z-index: 1001;
        }

        .fullscreen-close:hover {
            background: #c82333;
        }

        /* Zoom indicator */
        .zoom-indicator {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .diagram-wrapper:hover .zoom-indicator {
            opacity: 1;
        }
    </style>
</head>
<body>
    <h1>🤖 Robot World - Complete Design Documentation</h1>
    
    <div class="nav-container">
        <div class="nav-links">
            <a href="#architecture" class="nav-link">🏗️ Architecture</a>
            <a href="#user-experience" class="nav-link">👤 User Experience</a>
            <a href="#system-flows" class="nav-link">⚙️ System Flows</a>
            <a href="#interactions" class="nav-link">🔄 Interactions</a>
            <a href="#state-management" class="nav-link">📊 State Management</a>
        </div>
    </div>

    <div class="toc">
        <h3>📋 Complete Documentation Index</h3>
        <ul>'''
    
    # Add TOC entries
    for diagram_id, info in diagrams.items():
        html_content += f'\n            <li><a href="#{diagram_id}">{info["icon"]} {info["title"]}</a></li>'
    
    html_content += '''
        </ul>
    </div>

    <!-- Fullscreen Modal -->
    <div class="fullscreen-modal" id="fullscreen-modal">
        <div class="fullscreen-content">
            <button class="fullscreen-close" onclick="closeFullscreen()">×</button>
            <div id="fullscreen-diagram"></div>
        </div>
    </div>
'''
    
    # Group diagrams by category
    categories = {
        'architecture': '🏗️ System Architecture',
        'user-experience': '👤 User Experience',
        'system-flows': '⚙️ System Flows', 
        'interactions': '🔄 System Interactions',
        'state-management': '📊 State Management'
    }
    
    current_category = None
    
    for diagram_id, info in diagrams.items():
        # Add category header if needed
        if info['category'] != current_category:
            current_category = info['category']
            html_content += f'''
    <!-- {categories[current_category].upper()} -->
    <div class="category-header" id="{current_category}">
        <h2>{categories[current_category]}</h2>
        <p>{'Core structural diagrams showing the system architecture and design' if current_category == 'architecture' 
           else 'User journey and interaction flow diagrams' if current_category == 'user-experience'
           else 'Detailed system process flow diagrams' if current_category == 'system-flows'
           else 'Sequence diagrams showing component interactions' if current_category == 'interactions'
           else 'State diagrams showing system and component states'}</p>
    </div>
'''
        
        # Add diagram
        mermaid_content = read_mermaid_file(f'{diagram_id}.mmd')
        html_content += f'''
    <div class="diagram-container" id="{diagram_id}">
        <h2>{info["icon"]} {info["title"]}</h2>
        <div class="description">{info["description"]}</div>
        <div class="export-buttons">
            <a href="{diagram_id}.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="diagram-wrapper" data-diagram-id="{diagram_id}">
            <div class="zoom-controls">
                <button class="zoom-btn zoom-in" title="Zoom In">+</button>
                <button class="zoom-btn zoom-out" title="Zoom Out">−</button>
                <button class="zoom-btn reset-btn" title="Reset Zoom">⌂</button>
                <button class="zoom-btn fullscreen-btn" title="Fullscreen">⛶</button>
            </div>
            <div class="zoom-indicator">100%</div>
            <div class="mermaid" id="mermaid-{diagram_id}">
{mermaid_content}
            </div>
        </div>
    </div>
'''
    
    html_content += '''
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#007acc',
                primaryTextColor: '#333',
                primaryBorderColor: '#007acc',
                lineColor: '#666'
            }
        });

        // Zoom functionality for diagrams
        let zoomLevels = new Map(); // Store zoom level for each diagram
        let isDragging = false;
        let dragStart = { x: 0, y: 0 };
        let scrollStart = { x: 0, y: 0 };

        function initializeZoom() {
            const diagramWrappers = document.querySelectorAll('.diagram-wrapper');

            diagramWrappers.forEach(wrapper => {
                const diagramId = wrapper.dataset.diagramId;
                zoomLevels.set(diagramId, 1.0);

                // Zoom controls
                const zoomInBtn = wrapper.querySelector('.zoom-in');
                const zoomOutBtn = wrapper.querySelector('.zoom-out');
                const resetBtn = wrapper.querySelector('.reset-btn');
                const fullscreenBtn = wrapper.querySelector('.fullscreen-btn');

                zoomInBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    zoomDiagram(diagramId, 0.2);
                });

                zoomOutBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    zoomDiagram(diagramId, -0.2);
                });

                resetBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    resetZoom(diagramId);
                });

                fullscreenBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    openFullscreen(diagramId);
                });

                // Mouse wheel zoom
                wrapper.addEventListener('wheel', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        const delta = e.deltaY > 0 ? -0.1 : 0.1;
                        zoomDiagram(diagramId, delta);
                    }
                });

                // Pan functionality
                wrapper.addEventListener('mousedown', (e) => {
                    if (zoomLevels.get(diagramId) > 1) {
                        isDragging = true;
                        dragStart.x = e.clientX;
                        dragStart.y = e.clientY;
                        scrollStart.x = wrapper.scrollLeft;
                        scrollStart.y = wrapper.scrollTop;
                        wrapper.style.cursor = 'grabbing';
                    }
                });

                wrapper.addEventListener('mousemove', (e) => {
                    if (isDragging) {
                        e.preventDefault();
                        const deltaX = e.clientX - dragStart.x;
                        const deltaY = e.clientY - dragStart.y;
                        wrapper.scrollLeft = scrollStart.x - deltaX;
                        wrapper.scrollTop = scrollStart.y - deltaY;
                    }
                });

                wrapper.addEventListener('mouseup', () => {
                    isDragging = false;
                    wrapper.style.cursor = zoomLevels.get(diagramId) > 1 ? 'grab' : 'default';
                });

                wrapper.addEventListener('mouseleave', () => {
                    isDragging = false;
                    wrapper.style.cursor = zoomLevels.get(diagramId) > 1 ? 'grab' : 'default';
                });
            });
        }

        function zoomDiagram(diagramId, delta) {
            const currentZoom = zoomLevels.get(diagramId);
            const newZoom = Math.max(0.5, Math.min(3.0, currentZoom + delta));
            zoomLevels.set(diagramId, newZoom);

            const wrapper = document.querySelector(`[data-diagram-id="${diagramId}"]`);
            const mermaidElement = wrapper.querySelector('.mermaid');
            const indicator = wrapper.querySelector('.zoom-indicator');

            mermaidElement.style.transform = `scale(${newZoom})`;
            indicator.textContent = `${Math.round(newZoom * 100)}%`;

            // Add/remove zoomed class for styling
            if (newZoom > 1) {
                wrapper.classList.add('zoomed');
                wrapper.style.cursor = 'grab';
            } else {
                wrapper.classList.remove('zoomed');
                wrapper.style.cursor = 'default';
            }
        }

        function resetZoom(diagramId) {
            zoomLevels.set(diagramId, 1.0);
            const wrapper = document.querySelector(`[data-diagram-id="${diagramId}"]`);
            const mermaidElement = wrapper.querySelector('.mermaid');
            const indicator = wrapper.querySelector('.zoom-indicator');

            mermaidElement.style.transform = 'scale(1)';
            indicator.textContent = '100%';
            wrapper.classList.remove('zoomed');
            wrapper.style.cursor = 'default';
            wrapper.scrollLeft = 0;
            wrapper.scrollTop = 0;
        }

        function openFullscreen(diagramId) {
            const originalWrapper = document.querySelector(`[data-diagram-id="${diagramId}"]`);
            const originalMermaid = originalWrapper.querySelector('.mermaid');
            const modal = document.getElementById('fullscreen-modal');
            const fullscreenDiagram = document.getElementById('fullscreen-diagram');

            // Clone the diagram for fullscreen
            const clonedMermaid = originalMermaid.cloneNode(true);
            clonedMermaid.style.transform = 'scale(1)';
            clonedMermaid.style.maxWidth = 'none';
            clonedMermaid.style.maxHeight = 'none';

            fullscreenDiagram.innerHTML = '';
            fullscreenDiagram.appendChild(clonedMermaid);

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeFullscreen() {
            const modal = document.getElementById('fullscreen-modal');
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Close fullscreen on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeFullscreen();
            }
        });

        // Close fullscreen on background click
        document.getElementById('fullscreen-modal').addEventListener('click', (e) => {
            if (e.target.id === 'fullscreen-modal') {
                closeFullscreen();
            }
        });

        // Enhanced navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize zoom functionality
            initializeZoom();
            // Get all navigation links
            const navLinks = document.querySelectorAll('.nav-link, .toc a');
            const sections = document.querySelectorAll('.category-header, .diagram-container');

            // Add click handlers for smooth scrolling with proper offset
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // Calculate offset for sticky navigation
                        const offset = 120;
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - offset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });

                        // Update active states
                        updateActiveNavigation(targetId);
                    }
                });
            });

            // Function to update active navigation states
            function updateActiveNavigation(activeId) {
                // Remove all active classes
                navLinks.forEach(link => {
                    link.classList.remove('active');
                });

                // Add active class to current links
                navLinks.forEach(link => {
                    if (link.getAttribute('href') === '#' + activeId) {
                        link.classList.add('active');
                    }
                });
            }

            // Intersection Observer for automatic highlighting during scroll
            const observerOptions = {
                root: null,
                rootMargin: '-120px 0px -50% 0px',
                threshold: 0
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateActiveNavigation(entry.target.id);
                    }
                });
            }, observerOptions);

            // Observe all sections
            sections.forEach(section => {
                if (section.id) {
                    observer.observe(section);
                }
            });
        });

        // Make closeFullscreen globally accessible for the close button
        window.closeFullscreen = closeFullscreen;

        // Add keyboard shortcuts info
        console.log('🔍 Zoom Controls:');
        console.log('• Click +/- buttons to zoom in/out');
        console.log('• Ctrl/Cmd + Mouse Wheel to zoom');
        console.log('• Click house icon to reset zoom');
        console.log('• Click fullscreen icon for fullscreen view');
        console.log('• Drag to pan when zoomed in');
        console.log('• Press Escape to exit fullscreen');
    </script>
</body>
</html>'''
    
    # Write the HTML file
    with open('all-diagrams.html', 'w') as f:
        f.write(html_content)
    
    print("✅ Generated all-diagrams.html with all Robot World diagrams!")
    print("📂 File location: docs/diagrams/all-diagrams.html")
    print("🌐 Open in browser to view all diagrams")

if __name__ == "__main__":
    generate_html()
