<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2844.73828125 2213" style="max-width: 2844.73828125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf"><style>#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .error-icon{fill:#a44141;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .error-text{fill:#ddd;stroke:#ddd;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edge-thickness-normal{stroke-width:1px;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edge-thickness-thick{stroke-width:3.5px;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edge-pattern-solid{stroke-dasharray:0;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .marker.cross{stroke:lightgrey;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf p{margin:0;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .cluster-label text{fill:#F9FFFE;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .cluster-label span{color:#F9FFFE;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .cluster-label span p{background-color:transparent;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .label text,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf span{fill:#ccc;color:#ccc;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .node rect,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .node circle,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .node ellipse,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .node polygon,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .rough-node .label text,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .node .label text,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .image-shape .label,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .icon-shape .label{text-anchor:middle;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .rough-node .label,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .node .label,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .image-shape .label,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .icon-shape .label{text-align:center;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .node.clickable{cursor:pointer;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .arrowheadPath{fill:lightgrey;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .cluster text{fill:#F9FFFE;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .cluster span{color:#F9FFFE;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf rect.text{fill:none;stroke-width:0;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .icon-shape,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .icon-shape p,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .icon-shape rect,#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph7" class="cluster"><rect height="531" width="522.09375" y="8" x="8" style=""></rect><g transform="translate(208.3203125, 8)" class="cluster-label"><foreignObject height="24" width="121.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External Systems</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="2020" width="2286.64453125" y="185" x="550.09375" style=""></rect><g transform="translate(1628.181640625, 185)" class="cluster-label"><foreignObject height="24" width="130.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Server Application</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="152" width="575.3671875" y="1119" x="755.953125" style=""></rect><g transform="translate(981.00390625, 1119)" class="cluster-label"><foreignObject height="24" width="125.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Persistence Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="304" width="297.01171875" y="210" x="570.09375" style=""></rect><g transform="translate(648.013671875, 210)" class="cluster-label"><foreignObject height="24" width="141.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Configuration Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="682" width="661.19921875" y="1498" x="634.2109375" style=""></rect><g transform="translate(916.029296875, 1498)" class="cluster-label"><foreignObject height="24" width="97.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Domain Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="682" width="791" y="1321" x="2025.73828125" style=""></rect><g transform="translate(2352.44140625, 1321)" class="cluster-label"><foreignObject height="24" width="137.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Game Engine Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="1036" width="623.01953125" y="790" x="1382.71875" style=""></rect><g transform="translate(1597.384765625, 790)" class="cluster-label"><foreignObject height="24" width="193.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Command Processing Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="530" width="308.03125" y="210" x="1704.30078125" style=""></rect><g transform="translate(1806.19921875, 210)" class="cluster-label"><foreignObject height="24" width="104.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Network Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Clients_ServerSocket_0" d="M269.047,135L269.047,139.167C269.047,143.333,269.047,151.667,269.047,160C269.047,168.333,269.047,176.667,269.047,185C269.047,193.333,269.047,201.667,517.189,217.7C765.331,233.733,1261.615,257.465,1509.757,269.332L1757.899,281.198"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ServerSocket_ClientHandler_1" d="M1858.316,337L1858.316,341.167C1858.316,345.333,1858.316,353.667,1858.316,361.333C1858.316,369,1858.316,376,1858.316,379.5L1858.316,383"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClientHandler_MessageRouter_2" d="M1903.282,489L1906.956,493.167C1910.63,497.333,1917.977,505.667,1921.651,514C1925.324,522.333,1925.324,530.667,1925.324,541C1925.324,551.333,1925.324,563.667,1921.032,575.47C1916.741,587.273,1908.157,598.545,1903.865,604.181L1899.574,609.818"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MessageRouter_CommandParser_3" d="M1858.316,715L1858.316,719.167C1858.316,723.333,1858.316,731.667,1858.316,740C1858.316,748.333,1858.316,756.667,1858.316,765C1858.316,773.333,1858.316,781.667,1858.316,789.333C1858.316,797,1858.316,804,1858.316,807.5L1858.316,811"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CommandParser_CommandDispatcher_4" d="M1858.316,917L1858.316,921.167C1858.316,925.333,1858.316,933.667,1858.316,941.333C1858.316,949,1858.316,956,1858.316,959.5L1858.316,963"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CommandDispatcher_WorldCommandHandler_5" d="M1858.316,1069L1858.316,1073.167C1858.316,1077.333,1858.316,1085.667,1858.316,1094C1858.316,1102.333,1858.316,1110.667,1854.048,1118.561C1849.78,1126.456,1841.244,1133.912,1836.976,1137.64L1832.708,1141.369"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CommandDispatcher_RobotCommandHandler_6" d="M1752.449,1048.827L1726.593,1056.356C1700.737,1063.885,1649.025,1078.942,1623.169,1090.638C1597.313,1102.333,1597.313,1110.667,1597.313,1127.5C1597.313,1144.333,1597.313,1169.667,1597.313,1195C1597.313,1220.333,1597.313,1245.667,1597.313,1262.5C1597.313,1279.333,1597.313,1287.667,1597.313,1296C1597.313,1304.333,1597.313,1312.667,1597.313,1329.5C1597.313,1346.333,1597.313,1371.667,1597.313,1397C1597.313,1422.333,1597.313,1447.667,1597.313,1464.5C1597.313,1481.333,1597.313,1489.667,1597.313,1506.5C1597.313,1523.333,1597.313,1548.667,1597.313,1576C1597.313,1603.333,1597.313,1632.667,1597.313,1652.833C1597.313,1673,1597.313,1684,1597.313,1689.5L1597.313,1695"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WorldCommandHandler_WorldEngine_7" d="M1771.309,1246L1771.309,1250.167C1771.309,1254.333,1771.309,1262.667,1771.309,1271C1771.309,1279.333,1771.309,1287.667,1771.309,1296C1771.309,1304.333,1771.309,1312.667,1884.966,1327.552C1998.623,1342.438,2225.938,1363.876,2339.595,1374.595L2453.252,1385.314"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RobotCommandHandler_WorldEngine_8" d="M1716.172,1734.881L1811.665,1722.734C1907.158,1710.587,2098.143,1686.294,2193.636,1659.48C2289.129,1632.667,2289.129,1603.333,2289.129,1576C2289.129,1548.667,2289.129,1523.333,2289.129,1506.5C2289.129,1489.667,2289.129,1481.333,2316.502,1469.944C2343.875,1458.555,2398.621,1444.11,2425.994,1436.887L2453.367,1429.665"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RobotCommandHandler_MovementEngine_9" d="M1716.172,1788.533L1735.434,1794.778C1754.695,1801.022,1793.219,1813.511,1951.295,1823.922C2109.371,1834.333,2387,1842.667,2525.814,1850.333C2664.629,1858,2664.629,1865,2664.629,1868.5L2664.629,1872"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RobotCommandHandler_CombatEngine_10" d="M1656.289,1801L1661.107,1805.167C1665.926,1809.333,1675.563,1817.667,1799.669,1826C1923.775,1834.333,2162.35,1842.667,2281.638,1850.333C2400.926,1858,2400.926,1865,2400.926,1868.5L2400.926,1872"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RobotCommandHandler_VisibilityEngine_11" d="M1552.399,1801L1548.73,1805.167C1545.06,1809.333,1537.722,1817.667,1638.578,1826C1739.434,1834.333,1948.484,1842.667,2053.01,1850.333C2157.535,1858,2157.535,1865,2157.535,1868.5L2157.535,1872"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WorldEngine_WorldModel_12" d="M2547.205,1448L2544.758,1452.167C2542.31,1456.333,2537.415,1464.667,2534.967,1473C2532.52,1481.333,2532.52,1489.667,2532.52,1506.5C2532.52,1523.333,2532.52,1548.667,2532.52,1576C2532.52,1603.333,2532.52,1632.667,2532.52,1662C2532.52,1691.333,2532.52,1720.667,2532.52,1748C2532.52,1775.333,2532.52,1800.667,2532.52,1817.5C2532.52,1834.333,2532.52,1842.667,2532.52,1859.5C2532.52,1876.333,2532.52,1901.667,2532.52,1927C2532.52,1952.333,2532.52,1977.667,2263.593,1994.5C1994.667,2011.333,1456.814,2019.667,1181.49,2027.658C906.166,2035.649,893.371,2043.298,886.974,2047.123L880.577,2050.947"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WorldEngine_RobotManager_13" d="M2457.234,1426.59L2425.883,1434.325C2394.533,1442.06,2331.831,1457.53,2300.48,1469.432C2269.129,1481.333,2269.129,1489.667,2088.396,1505.416C1907.663,1521.165,1546.196,1544.33,1365.463,1555.912L1184.73,1567.495"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WorldEngine_ObstacleManager_14" d="M2697.094,1438.511L2713.701,1444.259C2730.309,1450.007,2763.523,1461.504,2780.131,1471.418C2796.738,1481.333,2796.738,1489.667,2796.738,1506.5C2796.738,1523.333,2796.738,1548.667,2796.738,1576C2796.738,1603.333,2796.738,1632.667,2796.738,1662C2796.738,1691.333,2796.738,1720.667,2796.738,1748C2796.738,1775.333,2796.738,1800.667,2796.738,1817.5C2796.738,1834.333,2796.738,1842.667,2796.738,1859.5C2796.738,1876.333,2796.738,1901.667,2796.738,1927C2796.738,1952.333,2796.738,1977.667,2533.465,1994.5C2270.191,2011.333,1743.645,2019.667,1472.687,2027.7C1201.73,2035.734,1186.363,2043.468,1178.679,2047.335L1170.996,2051.202"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MovementEngine_WorldModel_15" d="M2567.52,1950.092L2530.438,1958.91C2493.355,1967.728,2419.191,1985.364,2141.098,1998.349C1863.005,2011.333,1380.983,2019.667,1134.643,2027.614C888.302,2035.562,877.644,2043.124,872.314,2046.905L866.985,2050.686"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MovementEngine_ObstacleManager_16" d="M2664.629,1978L2664.629,1982.167C2664.629,1986.333,2664.629,1994.667,2404.047,2003C2143.465,2011.333,1622.301,2019.667,1360.076,2027.395C1097.852,2035.123,1094.567,2042.245,1092.925,2045.806L1091.282,2049.368"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CombatEngine_RobotManager_17" d="M2304.332,1921.657L2091.438,1909.881C1878.543,1898.105,1452.754,1874.552,1239.859,1858.609C1026.965,1842.667,1026.965,1834.333,1026.965,1817.5C1026.965,1800.667,1026.965,1775.333,1026.965,1748C1026.965,1720.667,1026.965,1691.333,1030.548,1671.062C1034.131,1650.79,1041.298,1639.58,1044.881,1633.975L1048.464,1628.37"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_VisibilityEngine_WorldModel_18" d="M2157.535,1978L2157.535,1982.167C2157.535,1986.333,2157.535,1994.667,1944.439,2003C1731.344,2011.333,1305.152,2019.667,1087.782,2027.562C870.413,2035.457,861.864,2042.914,857.59,2046.642L853.316,2050.371"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_VisibilityEngine_ObstacleManager_19" d="M2254.332,1943.68L2311.706,1953.567C2369.079,1963.453,2483.827,1983.227,2284.41,1997.28C2084.993,2011.333,1571.413,2019.667,1315.003,2027.337C1058.593,2035.008,1059.354,2042.016,1059.735,2045.519L1060.115,2049.023"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ConfigManager_ConfigFiles_20" d="M673.724,337L670.274,341.167C666.824,345.333,659.924,353.667,589.337,367.869C518.751,382.072,384.479,402.145,317.342,412.181L250.206,422.217"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ConfigManager_WorldFactory_21" d="M722.664,337L723.212,341.167C723.76,345.333,724.857,353.667,725.405,361.333C725.953,369,725.953,376,725.953,379.5L725.953,383"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WorldFactory_WorldModel_22" d="M690.435,489L687.533,493.167C684.631,497.333,678.827,505.667,675.925,514C673.023,522.333,673.023,530.667,673.023,541C673.023,551.333,673.023,563.667,673.023,584.5C673.023,605.333,673.023,634.667,673.023,662C673.023,689.333,673.023,714.667,673.023,731.5C673.023,748.333,673.023,756.667,673.023,765C673.023,773.333,673.023,781.667,673.023,798.5C673.023,815.333,673.023,840.667,673.023,866C673.023,891.333,673.023,916.667,673.023,942C673.023,967.333,673.023,992.667,673.023,1018C673.023,1043.333,673.023,1068.667,673.023,1085.5C673.023,1102.333,673.023,1110.667,673.023,1127.5C673.023,1144.333,673.023,1169.667,673.023,1195C673.023,1220.333,673.023,1245.667,700.976,1262.5C728.928,1279.333,784.833,1287.667,812.786,1296C840.738,1304.333,840.738,1312.667,840.738,1329.5C840.738,1346.333,840.738,1371.667,840.738,1397C840.738,1422.333,840.738,1447.667,840.738,1464.5C840.738,1481.333,840.738,1489.667,840.738,1506.5C840.738,1523.333,840.738,1548.667,840.738,1576C840.738,1603.333,840.738,1632.667,840.738,1662C840.738,1691.333,840.738,1720.667,840.738,1748C840.738,1775.333,840.738,1800.667,840.738,1817.5C840.738,1834.333,840.738,1842.667,840.738,1859.5C840.738,1876.333,840.738,1901.667,840.738,1927C840.738,1952.333,840.738,1977.667,840.738,1994.5C840.738,2011.333,840.738,2019.667,838.418,2027.439C836.098,2035.212,831.457,2042.424,829.137,2046.03L826.816,2049.636"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WorldFactory_ObstacleManager_23" d="M732.664,489L733.212,493.167C733.76,497.333,734.857,505.667,735.405,514C735.953,522.333,735.953,530.667,735.953,541C735.953,551.333,735.953,563.667,735.953,584.5C735.953,605.333,735.953,634.667,735.953,662C735.953,689.333,735.953,714.667,735.953,731.5C735.953,748.333,735.953,756.667,735.953,765C735.953,773.333,735.953,781.667,735.953,798.5C735.953,815.333,735.953,840.667,735.953,866C735.953,891.333,735.953,916.667,735.953,942C735.953,967.333,735.953,992.667,735.953,1018C735.953,1043.333,735.953,1068.667,735.953,1085.5C735.953,1102.333,735.953,1110.667,735.953,1127.5C735.953,1144.333,735.953,1169.667,735.953,1195C735.953,1220.333,735.953,1245.667,769.788,1262.5C803.622,1279.333,871.292,1287.667,905.126,1296C938.961,1304.333,938.961,1312.667,938.961,1329.5C938.961,1346.333,938.961,1371.667,938.961,1397C938.961,1422.333,938.961,1447.667,938.961,1464.5C938.961,1481.333,938.961,1489.667,938.961,1506.5C938.961,1523.333,938.961,1548.667,938.961,1576C938.961,1603.333,938.961,1632.667,938.961,1662C938.961,1691.333,938.961,1720.667,938.961,1748C938.961,1775.333,938.961,1800.667,938.961,1817.5C938.961,1834.333,938.961,1842.667,938.961,1859.5C938.961,1876.333,938.961,1901.667,938.961,1927C938.961,1952.333,938.961,1977.667,938.961,1994.5C938.961,2011.333,938.961,2019.667,945.358,2027.658C951.756,2035.649,964.55,2043.298,970.948,2047.123L977.345,2050.947"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_StateManager_WorldModel_24" d="M888.95,1246L882.576,1250.167C876.202,1254.333,863.455,1262.667,852.086,1271C840.717,1279.333,830.728,1287.667,825.733,1296C820.738,1304.333,820.738,1312.667,820.738,1329.5C820.738,1346.333,820.738,1371.667,820.738,1397C820.738,1422.333,820.738,1447.667,820.738,1464.5C820.738,1481.333,820.738,1489.667,820.738,1506.5C820.738,1523.333,820.738,1548.667,820.738,1576C820.738,1603.333,820.738,1632.667,820.738,1662C820.738,1691.333,820.738,1720.667,820.738,1748C820.738,1775.333,820.738,1800.667,820.738,1817.5C820.738,1834.333,820.738,1842.667,820.738,1859.5C820.738,1876.333,820.738,1901.667,820.738,1927C820.738,1952.333,820.738,1977.667,820.738,1994.5C820.738,2011.333,820.738,2019.667,819.391,2027.377C818.043,2035.087,815.348,2042.174,814,2045.718L812.653,2049.261"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_StateManager_RobotManager_25" d="M1044.98,1246L1051.354,1250.167C1057.728,1254.333,1070.475,1262.667,1076.849,1271C1083.223,1279.333,1083.223,1287.667,1083.223,1296C1083.223,1304.333,1083.223,1312.667,1083.223,1329.5C1083.223,1346.333,1083.223,1371.667,1083.223,1397C1083.223,1422.333,1083.223,1447.667,1083.223,1464.5C1083.223,1481.333,1083.223,1489.667,1083.223,1497.333C1083.223,1505,1083.223,1512,1083.223,1515.5L1083.223,1519"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogManager_WorldEngine_26" d="M1215.758,1246L1215.758,1250.167C1215.758,1254.333,1215.758,1262.667,1442.659,1271C1669.56,1279.333,2123.362,1287.667,2350.263,1296C2577.164,1304.333,2577.164,1312.667,2577.164,1320.333C2577.164,1328,2577.164,1335,2577.164,1338.5L2577.164,1342"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Console_WorldCommandHandler_27" d="M395.672,489L395.672,493.167C395.672,497.333,395.672,505.667,395.672,514C395.672,522.333,395.672,530.667,610.443,541C825.215,551.333,1254.758,563.667,1469.529,584.5C1684.301,605.333,1684.301,634.667,1684.301,662C1684.301,689.333,1684.301,714.667,1684.301,731.5C1684.301,748.333,1684.301,756.667,1684.301,765C1684.301,773.333,1684.301,781.667,1684.301,798.5C1684.301,815.333,1684.301,840.667,1684.301,866C1684.301,891.333,1684.301,916.667,1684.301,942C1684.301,967.333,1684.301,992.667,1684.301,1018C1684.301,1043.333,1684.301,1068.667,1684.301,1085.5C1684.301,1102.333,1684.301,1110.667,1688.569,1118.561C1692.837,1126.456,1701.373,1133.912,1705.641,1137.64L1709.909,1141.369"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MessageRouter_ClientHandler_28" d="M1835.49,613L1832.73,606.833C1829.97,600.667,1824.45,588.333,1821.69,576C1818.93,563.667,1818.93,551.333,1818.93,541C1818.93,530.667,1818.93,522.333,1820.782,514.592C1822.635,506.85,1826.34,499.701,1828.193,496.126L1830.045,492.551"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WorldModel_WorldCommandHandler_29" d="M745.659,2053L741.887,2048.833C738.114,2044.667,730.569,2036.333,726.796,2028C723.023,2019.667,723.023,2011.333,723.023,1994.5C723.023,1977.667,723.023,1952.333,723.023,1927C723.023,1901.667,723.023,1876.333,843.095,1859.5C963.167,1842.667,1203.31,1834.333,1323.382,1817.5C1443.453,1800.667,1443.453,1775.333,1443.453,1748C1443.453,1720.667,1443.453,1691.333,1443.453,1662C1443.453,1632.667,1443.453,1603.333,1443.453,1576C1443.453,1548.667,1443.453,1523.333,1443.453,1506.5C1443.453,1489.667,1443.453,1481.333,1443.453,1464.5C1443.453,1447.667,1443.453,1422.333,1443.453,1397C1443.453,1371.667,1443.453,1346.333,1443.453,1329.5C1443.453,1312.667,1443.453,1304.333,1443.453,1296C1443.453,1287.667,1443.453,1279.333,1477.617,1267.247C1511.781,1255.161,1580.108,1239.322,1614.272,1231.403L1648.435,1223.483"></path><path marker-end="url(#mermaid-7271c93d-a145-4959-91cc-b92f1f2a6fcf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RobotManager_RobotCommandHandler_30" d="M1113.038,1625L1116.643,1631.167C1120.248,1637.333,1127.458,1649.667,1187.706,1666.607C1247.953,1683.548,1361.238,1705.096,1417.881,1715.87L1474.524,1726.644"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1818.9296875, 576)" class="edgeLabel"><g transform="translate(-55.2421875, -12)" class="label"><foreignObject height="24" width="110.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>JSON responses</p></span></div></foreignObject></g></g><g transform="translate(1443.453125, 1662)" class="edgeLabel"><g transform="translate(-40.734375, -12)" class="label"><foreignObject height="24" width="81.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>world state</p></span></div></foreignObject></g></g><g transform="translate(1134.66796875, 1662)" class="edgeLabel"><g transform="translate(-43.125, -12)" class="label"><foreignObject height="24" width="86.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>robot states</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1858.31640625, 286)" id="flowchart-ServerSocket-309" class="node default"><rect height="102" width="192.84375" y="-51" x="-96.421875" style="" class="basic label-container"></rect><g transform="translate(-66.421875, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="132.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Server Socket<br>TCP listener<br>Client connections</p></span></div></foreignObject></g></g><g transform="translate(1858.31640625, 438)" id="flowchart-ClientHandler-310" class="node default"><rect height="102" width="238.03125" y="-51" x="-119.015625" style="" class="basic label-container"></rect><g transform="translate(-89.015625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="178.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Client Handler<br>Per-client threads<br>Connection management</p></span></div></foreignObject></g></g><g transform="translate(1858.31640625, 664)" id="flowchart-MessageRouter-311" class="node default"><rect height="102" width="191" y="-51" x="-95.5" style="" class="basic label-container"></rect><g transform="translate(-65.5, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="131"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Message Router<br>Request routing<br>Response handling</p></span></div></foreignObject></g></g><g transform="translate(1858.31640625, 866)" id="flowchart-CommandParser-312" class="node default"><rect height="102" width="206.6875" y="-51" x="-103.34375" style="" class="basic label-container"></rect><g transform="translate(-73.34375, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="146.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Command Parser<br>JSON parsing<br>Command validation</p></span></div></foreignObject></g></g><g transform="translate(1858.31640625, 1018)" id="flowchart-CommandDispatcher-313" class="node default"><rect height="102" width="211.734375" y="-51" x="-105.8671875" style="" class="basic label-container"></rect><g transform="translate(-75.8671875, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="151.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Command Dispatcher<br>Command routing<br>Handler selection</p></span></div></foreignObject></g></g><g transform="translate(1771.30859375, 1195)" id="flowchart-WorldCommandHandler-314" class="node default"><rect height="102" width="237.953125" y="-51" x="-118.9765625" style="" class="basic label-container"></rect><g transform="translate(-88.9765625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="177.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>World Command Handler<br>quit, robots, dump<br>World management</p></span></div></foreignObject></g></g><g transform="translate(1597.3125, 1750)" id="flowchart-RobotCommandHandler-315" class="node default"><rect height="102" width="237.71875" y="-51" x="-118.859375" style="" class="basic label-container"></rect><g transform="translate(-88.859375, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="177.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Robot Command Handler<br>Robot actions<br>Movement, combat</p></span></div></foreignObject></g></g><g transform="translate(2577.1640625, 1397)" id="flowchart-WorldEngine-316" class="node default"><rect height="102" width="239.859375" y="-51" x="-119.9296875" style="" class="basic label-container"></rect><g transform="translate(-89.9296875, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="179.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>World Engine<br>Game state management<br>Rule enforcement</p></span></div></foreignObject></g></g><g transform="translate(2664.62890625, 1927)" id="flowchart-MovementEngine-317" class="node default"><rect height="102" width="194.21875" y="-51" x="-97.109375" style="" class="basic label-container"></rect><g transform="translate(-67.109375, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="134.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Movement Engine<br>Position validation<br>Collision detection</p></span></div></foreignObject></g></g><g transform="translate(2400.92578125, 1927)" id="flowchart-CombatEngine-318" class="node default"><rect height="102" width="193.1875" y="-51" x="-96.59375" style="" class="basic label-container"></rect><g transform="translate(-66.59375, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="133.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Combat Engine<br>Weapon firing<br>Damage resolution</p></span></div></foreignObject></g></g><g transform="translate(2157.53515625, 1927)" id="flowchart-VisibilityEngine-319" class="node default"><rect height="102" width="193.59375" y="-51" x="-96.796875" style="" class="basic label-container"></rect><g transform="translate(-66.796875, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="133.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Visibility Engine<br>Line of sight<br>Range calculations</p></span></div></foreignObject></g></g><g transform="translate(791.8359375, 2104)" id="flowchart-WorldModel-320" class="node default"><rect height="102" width="245.25" y="-51" x="-122.625" style="" class="basic label-container"></rect><g transform="translate(-92.625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="185.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>World Model<br>Grid state<br>Robot &amp; obstacle tracking</p></span></div></foreignObject></g></g><g transform="translate(1083.22265625, 1574)" id="flowchart-RobotManager-321" class="node default"><rect height="102" width="195.03125" y="-51" x="-97.515625" style="" class="basic label-container"></rect><g transform="translate(-67.515625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="135.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Robot Manager<br>Robot lifecycle<br>State management</p></span></div></foreignObject></g></g><g transform="translate(1066.0859375, 2104)" id="flowchart-ObstacleManager-322" class="node default"><rect height="102" width="203.25" y="-51" x="-101.625" style="" class="basic label-container"></rect><g transform="translate(-71.625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="143.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Obstacle Manager<br>Obstacle definitions<br>Collision queries</p></span></div></foreignObject></g></g><g transform="translate(715.953125, 286)" id="flowchart-ConfigManager-323" class="node default"><rect height="102" width="221.71875" y="-51" x="-110.859375" style="" class="basic label-container"></rect><g transform="translate(-80.859375, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="161.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Configuration Manager<br>World settings<br>Parameter loading</p></span></div></foreignObject></g></g><g transform="translate(725.953125, 438)" id="flowchart-WorldFactory-324" class="node default"><rect height="102" width="202.890625" y="-51" x="-101.4453125" style="" class="basic label-container"></rect><g transform="translate(-71.4453125, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="142.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>World Factory<br>World initialization<br>Obstacle placement</p></span></div></foreignObject></g></g><g transform="translate(966.96484375, 1195)" id="flowchart-StateManager-325" class="node default"><rect height="102" width="229.015625" y="-51" x="-114.5078125" style="" class="basic label-container"></rect><g transform="translate(-84.5078125, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="169.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>State Manager<br>Game state persistence<br>Session management</p></span></div></foreignObject></g></g><g transform="translate(1215.7578125, 1195)" id="flowchart-LogManager-326" class="node default"><rect height="102" width="161.125" y="-51" x="-80.5625" style="" class="basic label-container"></rect><g transform="translate(-50.5625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="101.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Manager<br>Action logging<br>Audit trail</p></span></div></foreignObject></g></g><g transform="translate(144.625, 438)" id="flowchart-ConfigFiles-327" class="node default"><rect height="102" width="203.25" y="-51" x="-101.625" style="" class="basic label-container"></rect><g transform="translate(-71.625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="143.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Configuration Files<br>World parameters<br>Obstacle definitions</p></span></div></foreignObject></g></g><g transform="translate(269.046875, 84)" id="flowchart-Clients-328" class="node default"><rect height="102" width="177.671875" y="-51" x="-88.8359375" style="" class="basic label-container"></rect><g transform="translate(-58.8359375, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="117.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Robot Clients<br>TCP connections<br>JSON messages</p></span></div></foreignObject></g></g><g transform="translate(395.671875, 438)" id="flowchart-Console-329" class="node default"><rect height="102" width="198.84375" y="-51" x="-99.421875" style="" class="basic label-container"></rect><g transform="translate(-69.421875, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="138.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Server Console<br>Admin commands<br>World management</p></span></div></foreignObject></g></g></g></g></g></svg>