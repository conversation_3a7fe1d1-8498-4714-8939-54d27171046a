<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robot World - Complete Design Documentation</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            line-height: 1.6;
        }
        .diagram-container {
            background: white;
            margin: 30px 0;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #007acc;
        }
        .nav-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            position: sticky;
            top: 20px;
            z-index: 100;
        }
        .nav-links {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }
        .nav-link {
            background: #007acc;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.3s;
        }
        .nav-link:hover {
            background: #005a9e;
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #007acc;
            border-bottom: 2px solid #ddd;
            padding-bottom: 8px;
            margin-top: 0;
            font-size: 1.8em;
        }
        h3 {
            color: #333;
            margin-top: 25px;
            font-size: 1.3em;
        }
        .description {
            color: #666;
            font-style: italic;
            margin-bottom: 20px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid #007acc;
        }
        .mermaid {
            text-align: center;
            background: #fafbfc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .export-buttons {
            text-align: center;
            margin: 15px 0;
        }
        .export-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 8px;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background 0.3s;
        }
        .export-btn:hover {
            background: #218838;
        }
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 40px 0 20px 0;
            text-align: center;
        }
        .category-header h2 {
            margin: 0;
            border: none;
            color: white;
        }
        .toc {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin-top: 0;
            color: #495057;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #007acc;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            display: block;
            transition: background 0.3s;
        }
        .toc a:hover {
            background: #007acc;
            color: white;
        }
    </style>
</head>
<body>
    <h1>🤖 Robot World - Complete Design Documentation</h1>

    <div class="nav-container">
        <div class="nav-links">
            <a href="#architecture" class="nav-link">Architecture</a>
            <a href="#user-experience" class="nav-link">User Experience</a>
            <a href="#system-flows" class="nav-link">System Flows</a>
            <a href="#interactions" class="nav-link">Interactions</a>
            <a href="#state-management" class="nav-link">State Management</a>
        </div>
    </div>

    <div class="toc">
        <h3>📋 Table of Contents</h3>
        <ul>
            <li><a href="#class-diagram">Class Diagram - System Structure</a></li>
            <li><a href="#domain-diagram">Domain Diagram - Business Concepts</a></li>
            <li><a href="#client-component">Client Component Architecture</a></li>
            <li><a href="#server-component">Server Component Architecture</a></li>
            <li><a href="#user-journey">User Journey Storyboard</a></li>
            <li><a href="#user-story-flow">User Story Flow</a></li>
            <li><a href="#movement-flow">Robot Movement Flow</a></li>
            <li><a href="#combat-flow">Combat Resolution Flow</a></li>
            <li><a href="#world-management">World Management Flow</a></li>
            <li><a href="#launch-sequence">Robot Launch Sequence</a></li>
            <li><a href="#combat-sequence">Combat Sequence</a></li>
            <li><a href="#client-server">Client-Server Interaction</a></li>
            <li><a href="#robot-states">Robot State Diagram</a></li>
            <li><a href="#world-states">World State Diagram</a></li>
        </ul>
    </div>

    <!-- ARCHITECTURE DIAGRAMS -->
    <div class="category-header" id="architecture">
        <h2>🏗️ System Architecture</h2>
        <p>Core structural diagrams showing the system's architecture and design</p>
    </div>

    <div class="diagram-container" id="class-diagram">
        <h2>Class Diagram</h2>
        <div class="description">Shows the most important classes and their relationships in the Robot World system. This diagram defines the core object-oriented structure including World, Robot, Obstacles, Shield, Weapon, and their interactions.</div>
        <div class="export-buttons">
            <a href="class-diagram.mmd" class="export-btn" download>📄 Download Mermaid Source</a>
        </div>
        <div class="mermaid" id="class-diagram-viz">
classDiagram
    class World {
        -int width
        -int height
        -int visibilityRange
        -List~Robot~ robots
        -List~Obstacle~ obstacles
        -WorldConfiguration config
        +addRobot(Robot robot) boolean
        +removeRobot(String name) boolean
        +getRobots() List~Robot~
        +getObstacles() List~Obstacle~
        +isValidPosition(Position pos) boolean
        +isObstructed(Position pos) boolean
        +getRobotsInRange(Position pos, int range) List~Robot~
        +dump() String
    }

    class Robot {
        -String name
        -String make
        -Position position
        -Direction direction
        -Shield shield
        -Weapon weapon
        -RobotState state
        +move(int steps) boolean
        +turn(Direction dir) void
        +look() LookResult
        +fire() boolean
        +repair() void
        +reload() void
        +getState() RobotState
        +takeDamage() boolean
        +isAlive() boolean
    }

    class Position {
        -int x
        -int y
        +getX() int
        +getY() int
        +distanceTo(Position other) int
        +equals(Object obj) boolean
    }

    class Direction {
        &lt;&lt;enumeration&gt;&gt;
        NORTH
        SOUTH
        EAST
        WEST
        +turnLeft() Direction
        +turnRight() Direction
        +opposite() Direction
    }

    class Shield {
        -int currentStrength
        -int maxStrength
        -boolean repairing
        +takeDamage() boolean
        +repair(int repairTime) void
        +isDestroyed() boolean
        +getStrength() int
    }

    class Weapon {
        -int shots
        -int maxShots
        -int range
        -boolean reloading
        +fire() boolean
        +reload(int reloadTime) void
        +canFire() boolean
        +getShots() int
    }

    class Obstacle {
        &lt;&lt;abstract&gt;&gt;
        -Position topLeft
        -Position bottomRight
        +contains(Position pos) boolean
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Mountain {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Lake {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class BottomlessPit {
        +blocksMovement() boolean
        +blocksVision() boolean
        +destroysRobot() boolean
    }

    class WorldConfiguration {
        -int worldWidth
        -int worldHeight
        -int visibilityRange
        -int shieldRepairTime
        -int weaponReloadTime
        -int maxShieldStrength
        +loadFromFile(String filename) WorldConfiguration
    }

    class RobotState {
        -Position position
        -Direction direction
        -int shieldStrength
        -int shots
        -String status
    }

    class LookResult {
        -List~Obstacle~ obstacles
        -List~Robot~ robots
        -List~String~ edges
    }

    %% Relationships
    World "1" --&gt; "*" Robot : contains
    World "1" --&gt; "*" Obstacle : contains
    World "1" --&gt; "1" WorldConfiguration : uses
    Robot "1" --&gt; "1" Position : has
    Robot "1" --&gt; "1" Direction : faces
    Robot "1" --&gt; "1" Shield : has
    Robot "1" --&gt; "1" Weapon : has
    Robot "1" --&gt; "1" RobotState : provides
    Obstacle &lt;|-- Mountain : extends
    Obstacle &lt;|-- Lake : extends
    Obstacle &lt;|-- BottomlessPit : extends
    Obstacle "1" --&gt; "2" Position : defined by
    Robot --&gt; LookResult : creates
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#007acc',
                primaryTextColor: '#333',
                primaryBorderColor: '#007acc',
                lineColor: '#666'
            }
        });
    </script>
</body>
</html>
