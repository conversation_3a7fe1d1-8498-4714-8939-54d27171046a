<svg aria-roledescription="class" role="graphics-document document" viewBox="0 0 2789.912109375 1364" style="max-width: 2789.912109375px;" class="classDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa"><style>#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .error-icon{fill:#a44141;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .error-text{fill:#ddd;stroke:#ddd;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edge-thickness-normal{stroke-width:1px;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edge-thickness-thick{stroke-width:3.5px;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edge-pattern-solid{stroke-dasharray:0;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .marker.cross{stroke:lightgrey;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa p{margin:0;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa g.classGroup text{fill:#ccc;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa g.classGroup text .title{font-weight:bolder;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .nodeLabel,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edgeLabel{color:#e0dfdf;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edgeLabel .label rect{fill:#1f2020;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .label text{fill:#e0dfdf;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .labelBkg{background:#1f2020;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edgeLabel .label span{background:#1f2020;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .classTitle{font-weight:bolder;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .node rect,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .node circle,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .node ellipse,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .node polygon,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .divider{stroke:#ccc;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa g.clickable{cursor:pointer;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa g.classGroup rect{fill:#1f2020;stroke:#ccc;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa g.classGroup line{stroke:#ccc;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .classLabel .box{stroke:none;stroke-width:0;fill:#1f2020;opacity:0.5;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .classLabel .label{fill:#ccc;font-size:10px;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .relation{stroke:lightgrey;stroke-width:1;fill:none;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .dashed-line{stroke-dasharray:3;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .dotted-line{stroke-dasharray:1 2;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #compositionStart,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #compositionEnd,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #dependencyStart,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #dependencyStart,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #extensionStart,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #extensionEnd,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #aggregationStart,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #aggregationEnd,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #lollipopStart,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa #lollipopEnd,#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .edgeTerminals{font-size:11px;line-height:initial;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa .classTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker aggregation class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker aggregation class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker extension class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-extensionStart"><path d="M 1,7 L18,13 V 1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker extension class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker composition class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker composition class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="6" class="marker dependency class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="13" class="marker dependency class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="13" class="marker lollipop class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-lollipopStart"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="1" class="marker lollipop class" id="mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-lollipopEnd"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_World_Robot_1" d="M1883.076,270.958L1717.768,305.298C1552.46,339.639,1221.844,408.319,1056.536,447.826C891.229,487.333,891.229,497.667,891.229,502.833L891.229,508"></path><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_World_Obstacle_2" d="M2109.123,440L2109.123,446.167C2109.123,452.333,2109.123,464.667,2109.123,496C2109.123,527.333,2109.123,577.667,2109.123,602.833L2109.123,628"></path><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_World_WorldConfiguration_3" d="M2335.17,355.137L2370.18,375.447C2405.191,395.758,2475.212,436.379,2510.222,479.856C2545.232,523.333,2545.232,569.667,2545.232,592.833L2545.232,616"></path><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Robot_Position_4" d="M1010.197,812.053L1084.979,848.544C1159.761,885.035,1309.325,958.018,1377.459,1003.86C1445.593,1049.703,1432.298,1068.406,1425.65,1077.758L1419.002,1087.11"></path><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Robot_Direction_5" d="M772.26,797.351L665.392,836.292C558.523,875.234,344.787,953.117,237.919,997.225C131.051,1041.333,131.051,1051.667,131.051,1056.833L131.051,1062"></path><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Robot_Shield_6" d="M772.26,825.943L715.744,860.119C659.229,894.295,546.199,962.648,489.683,1003.991C433.168,1045.333,433.168,1059.667,433.168,1066.833L433.168,1074"></path><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Robot_Weapon_7" d="M772.26,982.902L768.093,990.919C763.927,998.935,755.594,1014.967,751.428,1028.15C747.262,1041.333,747.262,1051.667,747.262,1056.833L747.262,1062"></path><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Robot_RobotState_8" d="M1010.197,982.902L1014.364,990.919C1018.53,998.935,1026.863,1014.967,1031.029,1034.15C1035.195,1053.333,1035.195,1075.667,1035.195,1086.833L1035.195,1098"></path><path marker-start="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Obstacle_Mountain_9" d="M2033.019,889.7L2019.811,913.25C2006.604,936.8,1980.189,983.9,1966.981,1025.117C1953.773,1066.333,1953.773,1101.667,1953.773,1119.333L1953.773,1137"></path><path marker-start="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Obstacle_Lake_10" d="M2185.227,889.7L2198.435,913.25C2211.642,936.8,2238.058,983.9,2251.265,1025.117C2264.473,1066.333,2264.473,1101.667,2264.473,1119.333L2264.473,1137"></path><path marker-start="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Obstacle_BottomlessPit_11" d="M2274.097,850.332L2325.664,880.443C2377.231,910.555,2480.366,970.777,2531.933,1016.555C2583.5,1062.333,2583.5,1093.667,2583.5,1109.333L2583.5,1125"></path><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Obstacle_Position_12" d="M1959.693,800.912L1837.544,839.26C1715.395,877.608,1471.097,954.304,1354.261,1001.95C1237.425,1049.597,1248.051,1068.194,1253.365,1077.492L1258.678,1086.79"></path><path marker-end="url(#mermaid-d533ff60-2af8-4bc3-9555-de3449fc61fa_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Robot_LookResult_13" d="M1010.197,797.674L1116.128,836.562C1222.059,875.449,1433.92,953.225,1539.851,1007.279C1645.781,1061.333,1645.781,1091.667,1645.781,1106.833L1645.781,1122"></path></g><g class="edgeLabels"><g transform="translate(891.228515625, 477)" class="edgeLabel"><g transform="translate(-29.890625, -12)" class="label"><foreignObject height="24" width="59.78125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(2109.123046875, 477)" class="edgeLabel"><g transform="translate(-29.890625, -12)" class="label"><foreignObject height="24" width="59.78125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(2545.232421875, 477)" class="edgeLabel"><g transform="translate(-15.2109375, -12)" class="label"><foreignObject height="24" width="30.421875"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(1458.888671875, 1031)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(131.05078125, 1031)" class="edgeLabel"><g transform="translate(-18.7265625, -12)" class="label"><foreignObject height="24" width="37.453125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>faces</p></span></div></foreignObject></g></g><g transform="translate(433.16796875, 1031)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(747.26171875, 1031)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(1035.1953125, 1031)" class="edgeLabel"><g transform="translate(-30.1171875, -12)" class="label"><foreignObject height="24" width="60.234375"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>provides</p></span></div></foreignObject></g></g><g transform="translate(1953.7734375, 1031)" class="edgeLabel"><g transform="translate(-27.9765625, -12)" class="label"><foreignObject height="24" width="55.953125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>extends</p></span></div></foreignObject></g></g><g transform="translate(2264.47265625, 1031)" class="edgeLabel"><g transform="translate(-27.9765625, -12)" class="label"><foreignObject height="24" width="55.953125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>extends</p></span></div></foreignObject></g></g><g transform="translate(2583.5, 1031)" class="edgeLabel"><g transform="translate(-27.9765625, -12)" class="label"><foreignObject height="24" width="55.953125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>extends</p></span></div></foreignObject></g></g><g transform="translate(1226.798828125, 1031)" class="edgeLabel"><g transform="translate(-38.0625, -12)" class="label"><foreignObject height="24" width="76.125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>defined by</p></span></div></foreignObject></g></g><g transform="translate(1645.78125, 1031)" class="edgeLabel"><g transform="translate(-26.4140625, -12)" class="label"><foreignObject height="24" width="52.828125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates</p></span></div></foreignObject></g></g><g transform="translate(1862.8910713469932, 259.8309001091125)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2094.1230484375, 457.5000013392858)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2342.780084449795, 376.89277953553506)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1019.3465710386856, 833.2080919971846)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(750.6818504421677, 789.2487715954131)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(749.5229663868688, 822.1632696619939)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(750.8796310602796, 991.5128429029263)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1004.9580158410652, 1005.3478857352706)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1938.503892357707, 791.8429478540279)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(901.2285178125, 491.500001875)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">*</span></div></foreignObject></g><g transform="translate(2119.1230484375, 611.5000013392857)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">*</span></div></foreignObject></g><g transform="translate(2555.2324209375, 599.4999991964285)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(1432.8911647056761, 1081.4274380925397)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(141.05078062500002, 1045.4999994642858)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(443.167969375, 1057.5000005357142)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(757.261719375, 1045.5000005357142)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(1045.19531125, 1081.4999989285714)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(1260.996035054329, 1064.363747685888)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">2</span></div></foreignObject></g></g><g class="nodes"><g transform="translate(2109.123046875, 224)" id="classId-World-29" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-226.046875 -216 L226.046875 -216 L226.046875 216 L-226.046875 216"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-226.046875 -216 C-67.47036936928265 -216, 91.1061362614347 -216, 226.046875 -216 M-226.046875 -216 C-113.7391994993839 -216, -1.4315239987678012 -216, 226.046875 -216 M226.046875 -216 C226.046875 -101.69184636795694, 226.046875 12.616307264086117, 226.046875 216 M226.046875 -216 C226.046875 -64.95933549091436, 226.046875 86.08132901817129, 226.046875 216 M226.046875 216 C87.82749726128324 216, -50.39188047743352 216, -226.046875 216 M226.046875 216 C132.6028224437125 216, 39.158769887425024 216, -226.046875 216 M-226.046875 216 C-226.046875 107.01280016517131, -226.046875 -1.9743996696573731, -226.046875 -216 M-226.046875 216 C-226.046875 44.64934892212821, -226.046875 -126.70130215574358, -226.046875 -216"></path></g><g transform="translate(0, -192)" class="annotation-group text"></g><g transform="translate(-21.875, -192)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="43.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 93px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>World</p></span></div></foreignObject></g></g><g transform="translate(-214.046875, -144)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="70.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int width</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="75.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 130px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int height</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="134"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 188px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int visibilityRange</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="139.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 237px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-List&lt;Robot&gt; robots</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="181.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 282px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-List&lt;Obstacle&gt; obstacles</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="192.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 252px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-WorldConfiguration config</p></span></div></foreignObject></g></g><g transform="translate(-214.046875, 24)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="244.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 304px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+addRobot(Robot robot) : boolean</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="272.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 331px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+removeRobot(String name) : boolean</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="189.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 288px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getRobots() : List&lt;Robot&gt;</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="232.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 331px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getObstacles() : List&lt;Obstacle&gt;</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="278.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 339px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+isValidPosition(Position pos) : boolean</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="267.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 329px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+isObstructed(Position pos) : boolean</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="406.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 508px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getRobotsInRange(Position pos, int range) : List&lt;Robot&gt;</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="117.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 174px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+dump() : String</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-226.046875 -168 C-65.74959589448574 -168, 94.54768321102853 -168, 226.046875 -168 M-226.046875 -168 C-76.33533479496452 -168, 73.37620541007095 -168, 226.046875 -168"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-226.046875 0 C-64.927120908333 0, 96.19263318333401 0, 226.046875 0 M-226.046875 0 C-89.70621121203632 0, 46.634452575927355 0, 226.046875 0"></path></g></g><g transform="translate(891.228515625, 754)" id="classId-Robot-30" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-118.96875 -240 L118.96875 -240 L118.96875 240 L-118.96875 240"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-118.96875 -240 C-36.15350022374621 -240, 46.66174955250759 -240, 118.96875 -240 M-118.96875 -240 C-36.981935394504575 -240, 45.00487921099085 -240, 118.96875 -240 M118.96875 -240 C118.96875 -72.66803380834281, 118.96875 94.66393238331437, 118.96875 240 M118.96875 -240 C118.96875 -95.1049333631509, 118.96875 49.79013327369819, 118.96875 240 M118.96875 240 C62.01556811725798 240, 5.062386234515955 240, -118.96875 240 M118.96875 240 C63.59815459633232 240, 8.227559192664643 240, -118.96875 240 M-118.96875 240 C-118.96875 117.43060972467326, -118.96875 -5.138780550653479, -118.96875 -240 M-118.96875 240 C-118.96875 98.77700008237596, -118.96875 -42.445999835248074, -118.96875 -240"></path></g><g transform="translate(0, -216)" class="annotation-group text"></g><g transform="translate(-21.765625, -216)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="43.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 93px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Robot</p></span></div></foreignObject></g></g><g transform="translate(-106.96875, -168)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="91.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 149px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-String name</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="90.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-String make</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="123.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 182px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Position position</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="140.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 195px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Direction direction</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="96.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 154px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Shield shield</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="122.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 181px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Weapon weapon</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="125.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-RobotState state</p></span></div></foreignObject></g></g><g transform="translate(-106.96875, 24)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="192.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 248px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+move(int steps) : boolean</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="185.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 237px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+turn(Direction dir) : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="142.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 199px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+look() : LookResult</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="117.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+fire() : boolean</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="108.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 161px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+repair() : void</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="111.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 165px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+reload() : void</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="174.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 232px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getState() : RobotState</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="180.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 238px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+takeDamage() : boolean</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="138.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+isAlive() : boolean</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-118.96875 -192 C-39.42110103465804 -192, 40.12654793068393 -192, 118.96875 -192 M-118.96875 -192 C-40.35684812645444 -192, 38.255053747091125 -192, 118.96875 -192"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-118.96875 0 C-30.8620141335264 0, 57.2447217329472 0, 118.96875 0 M-118.96875 0 C-42.4602615843052 0, 34.0482268313896 0, 118.96875 0"></path></g></g><g transform="translate(1330.22265625, 1212)" id="classId-Position-31" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-142.12109375 -120 L142.12109375 -120 L142.12109375 120 L-142.12109375 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-142.12109375 -120 C-73.02516914102048 -120, -3.9292445320409684 -120, 142.12109375 -120 M-142.12109375 -120 C-56.81360640217811 -120, 28.49388094564378 -120, 142.12109375 -120 M142.12109375 -120 C142.12109375 -48.20130121845351, 142.12109375 23.597397563092983, 142.12109375 120 M142.12109375 -120 C142.12109375 -43.41692566056557, 142.12109375 33.166148678868865, 142.12109375 120 M142.12109375 120 C68.22327253942547 120, -5.6745486711490685 120, -142.12109375 120 M142.12109375 120 C70.48230972430667 120, -1.1564743013866519 120, -142.12109375 120 M-142.12109375 120 C-142.12109375 65.56849663342794, -142.12109375 11.136993266855882, -142.12109375 -120 M-142.12109375 120 C-142.12109375 39.58036724116134, -142.12109375 -40.83926551767732, -142.12109375 -120"></path></g><g transform="translate(0, -96)" class="annotation-group text"></g><g transform="translate(-29.4921875, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="58.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 108px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Position</p></span></div></foreignObject></g></g><g transform="translate(-130.12109375, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="38.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 92px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int x</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="38.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 93px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int y</p></span></div></foreignObject></g></g><g transform="translate(-130.12109375, 24)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="87.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 141px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getX() : int</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="87.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 141px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getY() : int</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="230.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 285px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+distanceTo(Position other) : int</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="215.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 269px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+equals(Object obj) : boolean</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-142.12109375 -72 C-84.44791559470882 -72, -26.774737439417635 -72, 142.12109375 -72 M-142.12109375 -72 C-75.33103981045817 -72, -8.54098587091633 -72, 142.12109375 -72"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-142.12109375 0 C-50.233685084549265 0, 41.65372358090147 0, 142.12109375 0 M-142.12109375 0 C-40.13272337697518 0, 61.85564699604964 0, 142.12109375 0"></path></g></g><g transform="translate(131.05078125, 1212)" id="classId-Direction-32" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-123.05078125 -144 L123.05078125 -144 L123.05078125 144 L-123.05078125 144"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-123.05078125 -144 C-48.98360927957853 -144, 25.08356269084294 -144, 123.05078125 -144 M-123.05078125 -144 C-62.76004694133829 -144, -2.4693126326765764 -144, 123.05078125 -144 M123.05078125 -144 C123.05078125 -84.32771551380738, 123.05078125 -24.655431027614753, 123.05078125 144 M123.05078125 -144 C123.05078125 -44.49651075870992, 123.05078125 55.006978482580166, 123.05078125 144 M123.05078125 144 C61.417917712444904 144, -0.2149458251101919 144, -123.05078125 144 M123.05078125 144 C36.77133596018051 144, -49.508109329638984 144, -123.05078125 144 M-123.05078125 144 C-123.05078125 58.859872256830684, -123.05078125 -26.280255486338632, -123.05078125 -144 M-123.05078125 144 C-123.05078125 75.26955133458715, -123.05078125 6.539102669174298, -123.05078125 -144"></path></g><g transform="translate(-53.9296875, -120)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="107.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 161px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«enumeration»</p></span></div></foreignObject></g></g><g transform="translate(-34.4453125, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="68.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Direction</p></span></div></foreignObject></g></g><g transform="translate(-111.05078125, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="49.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 105px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>NORTH</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="48.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 105px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>SOUTH</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="35"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 89px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>EAST</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="39.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 94px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>WEST</p></span></div></foreignObject></g></g><g transform="translate(-111.05078125, 72)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="160.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 212px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+turnLeft() : Direction</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="168.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 221px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+turnRight() : Direction</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="162.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 217px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+opposite() : Direction</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-123.05078125 -72 C-56.96498771573981 -72, 9.120805818520381 -72, 123.05078125 -72 M-123.05078125 -72 C-36.95605832613798 -72, 49.138664597724045 -72, 123.05078125 -72"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-123.05078125 48 C-58.74317749157879 48, 5.564426266842418 48, 123.05078125 48 M-123.05078125 48 C-38.961593656725725 48, 45.12759393654855 48, 123.05078125 48"></path></g></g><g transform="translate(433.16796875, 1212)" id="classId-Shield-33" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-129.06640625 -132 L129.06640625 -132 L129.06640625 132 L-129.06640625 132"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-129.06640625 -132 C-48.32447609575935 -132, 32.4174540584813 -132, 129.06640625 -132 M-129.06640625 -132 C-65.98494810971803 -132, -2.9034899694360803 -132, 129.06640625 -132 M129.06640625 -132 C129.06640625 -58.266839687980436, 129.06640625 15.466320624039128, 129.06640625 132 M129.06640625 -132 C129.06640625 -43.62388023549829, 129.06640625 44.75223952900342, 129.06640625 132 M129.06640625 132 C45.169814628873596 132, -38.72677699225281 132, -129.06640625 132 M129.06640625 132 C50.90079236237909 132, -27.264821525241814 132, -129.06640625 132 M-129.06640625 132 C-129.06640625 63.88264929406023, -129.06640625 -4.234701411879541, -129.06640625 -132 M-129.06640625 132 C-129.06640625 45.019421491646, -129.06640625 -41.961157016708, -129.06640625 -132"></path></g><g transform="translate(0, -108)" class="annotation-group text"></g><g transform="translate(-22.8203125, -108)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="45.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 95px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Shield</p></span></div></foreignObject></g></g><g transform="translate(-117.06640625, -60)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="144.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int currentStrength</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="120.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 177px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int maxStrength</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="131.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 188px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-boolean repairing</p></span></div></foreignObject></g></g><g transform="translate(-117.06640625, 36)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="180.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 238px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+takeDamage() : boolean</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="211.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 260px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+repair(int repairTime) : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="175.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 233px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+isDestroyed() : boolean</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="139.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 194px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getStrength() : int</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-129.06640625 -84 C-46.446260915060776 -84, 36.17388441987845 -84, 129.06640625 -84 M-129.06640625 -84 C-31.413894129170245 -84, 66.23861799165951 -84, 129.06640625 -84"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-129.06640625 12 C-51.61890382660944 12, 25.82859859678112 12, 129.06640625 12 M-129.06640625 12 C-70.52476575536859 12, -11.983125260737182 12, 129.06640625 12"></path></g></g><g transform="translate(747.26171875, 1212)" id="classId-Weapon-34" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-135.02734375 -144 L135.02734375 -144 L135.02734375 144 L-135.02734375 144"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-135.02734375 -144 C-60.91704907314099 -144, 13.193245603718026 -144, 135.02734375 -144 M-135.02734375 -144 C-36.393728367718296 -144, 62.23988701456341 -144, 135.02734375 -144 M135.02734375 -144 C135.02734375 -71.77075483327097, 135.02734375 0.4584903334580588, 135.02734375 144 M135.02734375 -144 C135.02734375 -53.761907656053054, 135.02734375 36.47618468789389, 135.02734375 144 M135.02734375 144 C51.794032572260534 144, -31.439278605478933 144, -135.02734375 144 M135.02734375 144 C51.22122399571991 144, -32.58489575856018 144, -135.02734375 144 M-135.02734375 144 C-135.02734375 69.49570863873774, -135.02734375 -5.0085827225245225, -135.02734375 -144 M-135.02734375 144 C-135.02734375 49.74881816769647, -135.02734375 -44.50236366460706, -135.02734375 -144"></path></g><g transform="translate(0, -120)" class="annotation-group text"></g><g transform="translate(-29.6953125, -120)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="59.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 109px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Weapon</p></span></div></foreignObject></g></g><g transform="translate(-123.02734375, -72)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="66.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int shots</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="97.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 156px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int maxShots</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="70.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 126px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int range</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="134.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-boolean reloading</p></span></div></foreignObject></g></g><g transform="translate(-123.02734375, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="117.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+fire() : boolean</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="216.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 268px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+reload(int reloadTime) : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="145.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+canFire() : boolean</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="116.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 172px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getShots() : int</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-135.02734375 -96 C-34.91865252500652 -96, 65.19003869998696 -96, 135.02734375 -96 M-135.02734375 -96 C-37.7299225714396 -96, 59.5674986071208 -96, 135.02734375 -96"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-135.02734375 24 C-74.21530354723137 24, -13.403263344462744 24, 135.02734375 24 M-135.02734375 24 C-53.45902429775951 24, 28.109295154480975 24, 135.02734375 24"></path></g></g><g transform="translate(2109.123046875, 754)" id="classId-Obstacle-35" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-149.4296875 -120 L149.4296875 -120 L149.4296875 120 L-149.4296875 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-149.4296875 -120 C-41.931278479357374 -120, 65.56713054128525 -120, 149.4296875 -120 M-149.4296875 -120 C-40.5393133893439 -120, 68.3510607213122 -120, 149.4296875 -120 M149.4296875 -120 C149.4296875 -63.86608272664661, 149.4296875 -7.73216545329322, 149.4296875 120 M149.4296875 -120 C149.4296875 -24.40746250277809, 149.4296875 71.18507499444382, 149.4296875 120 M149.4296875 120 C33.660639829964666 120, -82.10840784007067 120, -149.4296875 120 M149.4296875 120 C76.74704438605632 120, 4.064401272112633 120, -149.4296875 120 M-149.4296875 120 C-149.4296875 46.92137641987311, -149.4296875 -26.157247160253775, -149.4296875 -120 M-149.4296875 120 C-149.4296875 25.017799159680663, -149.4296875 -69.96440168063867, -149.4296875 -120"></path></g><g transform="translate(-37.90625, -96)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="75.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«abstract»</p></span></div></foreignObject></g></g><g transform="translate(-32.2109375, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="64.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Obstacle</p></span></div></foreignObject></g></g><g transform="translate(-137.4296875, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="119.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 176px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Position topLeft</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="155.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 214px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Position bottomRight</p></span></div></foreignObject></g></g><g transform="translate(-137.4296875, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="236.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 297px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+contains(Position pos) : boolean</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="210.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 270px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+blocksMovement() : boolean</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="179.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 237px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+blocksVision() : boolean</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-149.4296875 -48 C-60.61749135885587 -48, 28.194704782288255 -48, 149.4296875 -48 M-149.4296875 -48 C-32.15343842084701 -48, 85.12281065830598 -48, 149.4296875 -48"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-149.4296875 24 C-75.35531759712397 24, -1.280947694247942 24, 149.4296875 24 M-149.4296875 24 C-88.14421874731599 24, -26.858749994631992 24, 149.4296875 24"></path></g></g><g transform="translate(1953.7734375, 1212)" id="classId-Mountain-36" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-134.5546875 -75 L134.5546875 -75 L134.5546875 75 L-134.5546875 75"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-134.5546875 -75 C-48.6617681337916 -75, 37.231151232416806 -75, 134.5546875 -75 M-134.5546875 -75 C-64.1631575126631 -75, 6.22837247467379 -75, 134.5546875 -75 M134.5546875 -75 C134.5546875 -32.88078495766068, 134.5546875 9.23843008467864, 134.5546875 75 M134.5546875 -75 C134.5546875 -40.15538148301654, 134.5546875 -5.3107629660330815, 134.5546875 75 M134.5546875 75 C33.118749196192255 75, -68.31718910761549 75, -134.5546875 75 M134.5546875 75 C41.49652141456677 75, -51.56164467086646 75, -134.5546875 75 M-134.5546875 75 C-134.5546875 22.597608660553746, -134.5546875 -29.804782678892508, -134.5546875 -75 M-134.5546875 75 C-134.5546875 34.62068004461209, -134.5546875 -5.758639910775827, -134.5546875 -75"></path></g><g transform="translate(0, -51)" class="annotation-group text"></g><g transform="translate(-34.484375, -51)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="68.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Mountain</p></span></div></foreignObject></g></g><g transform="translate(-122.5546875, -3)" class="members-group text"></g><g transform="translate(-122.5546875, 27)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="210.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 270px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+blocksMovement() : boolean</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="179.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 237px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+blocksVision() : boolean</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-134.5546875 -27 C-62.94474458390475 -27, 8.6651983321905 -27, 134.5546875 -27 M-134.5546875 -27 C-50.13025053801438 -27, 34.29418642397124 -27, 134.5546875 -27"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-134.5546875 -3 C-36.40579194427252 -3, 61.743103611454956 -3, 134.5546875 -3 M-134.5546875 -3 C-57.62335202621745 -3, 19.307983447565107 -3, 134.5546875 -3"></path></g></g><g transform="translate(2264.47265625, 1212)" id="classId-Lake-37" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-126.14453125 -75 L126.14453125 -75 L126.14453125 75 L-126.14453125 75"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.14453125 -75 C-30.951250553028032 -75, 64.24203014394394 -75, 126.14453125 -75 M-126.14453125 -75 C-27.98865631590087 -75, 70.16721861819826 -75, 126.14453125 -75 M126.14453125 -75 C126.14453125 -38.28004951041651, 126.14453125 -1.5600990208330217, 126.14453125 75 M126.14453125 -75 C126.14453125 -43.07894852931179, 126.14453125 -11.157897058623568, 126.14453125 75 M126.14453125 75 C62.93756016632123 75, -0.26941091735754696 75, -126.14453125 75 M126.14453125 75 C66.46811749258819 75, 6.791703735176384 75, -126.14453125 75 M-126.14453125 75 C-126.14453125 37.635724356590096, -126.14453125 0.2714487131801917, -126.14453125 -75 M-126.14453125 75 C-126.14453125 19.599749200573115, -126.14453125 -35.80050159885377, -126.14453125 -75"></path></g><g transform="translate(0, -51)" class="annotation-group text"></g><g transform="translate(-17.6640625, -51)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="35.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 84px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Lake</p></span></div></foreignObject></g></g><g transform="translate(-114.14453125, -3)" class="members-group text"></g><g transform="translate(-114.14453125, 27)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="210.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 270px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+blocksMovement() : boolean</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="179.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 237px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+blocksVision() : boolean</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.14453125 -27 C-75.65557817286512 -27, -25.166625095730225 -27, 126.14453125 -27 M-126.14453125 -27 C-27.589410732423033 -27, 70.96570978515393 -27, 126.14453125 -27"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.14453125 -3 C-29.525521073637222 -3, 67.09348910272556 -3, 126.14453125 -3 M-126.14453125 -3 C-53.2802587623536 -3, 19.584013725292806 -3, 126.14453125 -3"></path></g></g><g transform="translate(2583.5, 1212)" id="classId-BottomlessPit-38" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-142.8828125 -87 L142.8828125 -87 L142.8828125 87 L-142.8828125 87"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-142.8828125 -87 C-85.31104829717779 -87, -27.739284094355597 -87, 142.8828125 -87 M-142.8828125 -87 C-82.6288264253082 -87, -22.37484035061641 -87, 142.8828125 -87 M142.8828125 -87 C142.8828125 -19.11756974677806, 142.8828125 48.76486050644388, 142.8828125 87 M142.8828125 -87 C142.8828125 -21.74706395000935, 142.8828125 43.5058720999813, 142.8828125 87 M142.8828125 87 C58.427924133058355 87, -26.02696423388329 87, -142.8828125 87 M142.8828125 87 C32.386870571511594 87, -78.10907135697681 87, -142.8828125 87 M-142.8828125 87 C-142.8828125 20.598365282506975, -142.8828125 -45.80326943498605, -142.8828125 -87 M-142.8828125 87 C-142.8828125 28.013125698229608, -142.8828125 -30.973748603540784, -142.8828125 -87"></path></g><g transform="translate(0, -63)" class="annotation-group text"></g><g transform="translate(-51.140625, -63)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="102.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 151px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>BottomlessPit</p></span></div></foreignObject></g></g><g transform="translate(-130.8828125, -15)" class="members-group text"></g><g transform="translate(-130.8828125, 15)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="210.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 270px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+blocksMovement() : boolean</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="179.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 237px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+blocksVision() : boolean</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="193.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 252px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+destroysRobot() : boolean</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-142.8828125 -39 C-41.67845535641199 -39, 59.525901787176025 -39, 142.8828125 -39 M-142.8828125 -39 C-64.94660452309864 -39, 12.989603453802715 -39, 142.8828125 -39"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-142.8828125 -15 C-85.11638756074078 -15, -27.349962621481552 -15, 142.8828125 -15 M-142.8828125 -15 C-36.10197890365366 -15, 70.67885469269268 -15, 142.8828125 -15"></path></g></g><g transform="translate(2545.232421875, 754)" id="classId-WorldConfiguration-39" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-236.6796875 -132 L236.6796875 -132 L236.6796875 132 L-236.6796875 132"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-236.6796875 -132 C-90.45167590313088 -132, 55.77633569373825 -132, 236.6796875 -132 M-236.6796875 -132 C-81.13438025826846 -132, 74.41092698346307 -132, 236.6796875 -132 M236.6796875 -132 C236.6796875 -70.34098957702183, 236.6796875 -8.68197915404366, 236.6796875 132 M236.6796875 -132 C236.6796875 -73.49339541355076, 236.6796875 -14.98679082710153, 236.6796875 132 M236.6796875 132 C68.70805633505296 132, -99.26357482989408 132, -236.6796875 132 M236.6796875 132 C110.22678974439349 132, -16.22610801121303 132, -236.6796875 132 M-236.6796875 132 C-236.6796875 48.65310261030133, -236.6796875 -34.69379477939734, -236.6796875 -132 M-236.6796875 132 C-236.6796875 58.73172042696753, -236.6796875 -14.536559146064945, -236.6796875 -132"></path></g><g transform="translate(0, -108)" class="annotation-group text"></g><g transform="translate(-72.34375, -108)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="144.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>WorldConfiguration</p></span></div></foreignObject></g></g><g transform="translate(-224.6796875, -60)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="112.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int worldWidth</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="117.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 172px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int worldHeight</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="134"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 188px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int visibilityRange</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="153.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 208px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int shieldRepairTime</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="168.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 226px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int weaponReloadTime</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="164.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 222px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int maxShieldStrength</p></span></div></foreignObject></g></g><g transform="translate(-224.6796875, 108)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="377.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 432px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+loadFromFile(String filename) : WorldConfiguration</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-236.6796875 -84 C-106.8510976794993 -84, 22.97749214100139 -84, 236.6796875 -84 M-236.6796875 -84 C-134.8915132737789 -84, -33.103339047557796 -84, 236.6796875 -84"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-236.6796875 84 C-98.0591694015097 84, 40.56134869698059 84, 236.6796875 84 M-236.6796875 84 C-124.57558251610034 84, -12.47147753220068 84, 236.6796875 84"></path></g></g><g transform="translate(1035.1953125, 1212)" id="classId-RobotState-40" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-102.90625 -108 L102.90625 -108 L102.90625 108 L-102.90625 108"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-102.90625 -108 C-57.737538364295226 -108, -12.568826728590452 -108, 102.90625 -108 M-102.90625 -108 C-58.88386813231234 -108, -14.861486264624673 -108, 102.90625 -108 M102.90625 -108 C102.90625 -40.59990410097585, 102.90625 26.800191798048303, 102.90625 108 M102.90625 -108 C102.90625 -27.85174385202737, 102.90625 52.29651229594526, 102.90625 108 M102.90625 108 C31.123838477635488 108, -40.658573044729025 108, -102.90625 108 M102.90625 108 C43.281386240413866 108, -16.343477519172268 108, -102.90625 108 M-102.90625 108 C-102.90625 29.961582505733645, -102.90625 -48.07683498853271, -102.90625 -108 M-102.90625 108 C-102.90625 24.93524228749655, -102.90625 -58.1295154250069, -102.90625 -108"></path></g><g transform="translate(0, -84)" class="annotation-group text"></g><g transform="translate(-41.0625, -84)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="82.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>RobotState</p></span></div></foreignObject></g></g><g transform="translate(-90.90625, -36)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="123.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 182px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Position position</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="140.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 195px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Direction direction</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="133.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 190px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int shieldStrength</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="66.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int shots</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="95.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 154px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-String status</p></span></div></foreignObject></g></g><g transform="translate(-90.90625, 108)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-102.90625 -60 C-44.15901516379513 -60, 14.588219672409735 -60, 102.90625 -60 M-102.90625 -60 C-61.31973453887355 -60, -19.733219077747094 -60, 102.90625 -60"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-102.90625 84 C-52.80459892644932 84, -2.702947852898646 84, 102.90625 84 M-102.90625 84 C-24.72234271483184 84, 53.46156457033632 84, 102.90625 84"></path></g></g><g transform="translate(1645.78125, 1212)" id="classId-LookResult-41" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-123.4375 -84 L123.4375 -84 L123.4375 84 L-123.4375 84"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-123.4375 -84 C-50.77758736560132 -84, 21.88232526879736 -84, 123.4375 -84 M-123.4375 -84 C-64.98953507955261 -84, -6.54157015910522 -84, 123.4375 -84 M123.4375 -84 C123.4375 -44.10499098373925, 123.4375 -4.209981967478498, 123.4375 84 M123.4375 -84 C123.4375 -35.34950158540259, 123.4375 13.300996829194816, 123.4375 84 M123.4375 84 C58.48548973745609 84, -6.466520525087816 84, -123.4375 84 M123.4375 84 C32.28132049096793 84, -58.87485901806414 84, -123.4375 84 M-123.4375 84 C-123.4375 21.379059535826883, -123.4375 -41.241880928346234, -123.4375 -84 M-123.4375 84 C-123.4375 31.537672980379362, -123.4375 -20.924654039241275, -123.4375 -84"></path></g><g transform="translate(0, -60)" class="annotation-group text"></g><g transform="translate(-41.046875, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="82.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 130px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>LookResult</p></span></div></foreignObject></g></g><g transform="translate(-111.4375, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="181.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 282px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-List&lt;Obstacle&gt; obstacles</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="139.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 237px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-List&lt;Robot&gt; robots</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="135.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 235px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-List&lt;String&gt; edges</p></span></div></foreignObject></g></g><g transform="translate(-111.4375, 84)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-123.4375 -36 C-33.08124786201263 -36, 57.27500427597474 -36, 123.4375 -36 M-123.4375 -36 C-57.010968924029655 -36, 9.41556215194069 -36, 123.4375 -36"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-123.4375 60 C-35.23839198442697 60, 52.96071603114606 60, 123.4375 60 M-123.4375 60 C-46.0758110622522 60, 31.2858778754956 60, 123.4375 60"></path></g></g></g></g></g></svg>