classDiagram
    class World {
        -int width
        -int height
        -int visibilityRange
        -List~Robot~ robots
        -List~Obstacle~ obstacles
        -WorldConfiguration config
        +addRobot(Robot robot) boolean
        +removeRobot(String name) boolean
        +getRobots() List~Robot~
        +getObstacles() List~Obstacle~
        +isValidPosition(Position pos) boolean
        +isObstructed(Position pos) boolean
        +getRobotsInRange(Position pos, int range) List~Robot~
        +dump() String
    }

    class Robot {
        -String name
        -String make
        -Position position
        -Direction direction
        -Shield shield
        -Weapon weapon
        -RobotState state
        +move(int steps) boolean
        +turn(Direction dir) void
        +look() LookResult
        +fire() boolean
        +repair() void
        +reload() void
        +getState() RobotState
        +takeDamage() boolean
        +isAlive() boolean
    }

    class Position {
        -int x
        -int y
        +getX() int
        +getY() int
        +distanceTo(Position other) int
        +equals(Object obj) boolean
    }

    class Direction {
        <<enumeration>>
        NORTH
        SOUTH
        EAST
        WEST
        +turnLeft() Direction
        +turnRight() Direction
        +opposite() Direction
    }

    class Shield {
        -int currentStrength
        -int maxStrength
        -boolean repairing
        +takeDamage() boolean
        +repair(int repairTime) void
        +isDestroyed() boolean
        +getStrength() int
    }

    class Weapon {
        -int shots
        -int maxShots
        -int range
        -boolean reloading
        +fire() boolean
        +reload(int reloadTime) void
        +canFire() boolean
        +getShots() int
    }

    class Obstacle {
        <<abstract>>
        -Position topLeft
        -Position bottomRight
        +contains(Position pos) boolean
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Mountain {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Lake {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class BottomlessPit {
        +blocksMovement() boolean
        +blocksVision() boolean
        +destroysRobot() boolean
    }

    class WorldConfiguration {
        -int worldWidth
        -int worldHeight
        -int visibilityRange
        -int shieldRepairTime
        -int weaponReloadTime
        -int maxShieldStrength
        +loadFromFile(String filename) WorldConfiguration
    }

    class RobotState {
        -Position position
        -Direction direction
        -int shieldStrength
        -int shots
        -String status
    }

    class LookResult {
        -List~Obstacle~ obstacles
        -List~Robot~ robots
        -List~String~ edges
    }

    %% Relationships
    World "1" --> "*" Robot : contains
    World "1" --> "*" Obstacle : contains
    World "1" --> "1" WorldConfiguration : uses
    Robot "1" --> "1" Position : has
    Robot "1" --> "1" Direction : faces
    Robot "1" --> "1" Shield : has
    Robot "1" --> "1" Weapon : has
    Robot "1" --> "1" RobotState : provides
    Obstacle <|-- Mountain : extends
    Obstacle <|-- Lake : extends
    Obstacle <|-- BottomlessPit : extends
    Obstacle "1" --> "2" Position : defined by
    Robot --> LookResult : creates
